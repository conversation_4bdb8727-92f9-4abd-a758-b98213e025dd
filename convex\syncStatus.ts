import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const updateSyncStatus = mutation({
  args: {
    userId: v.id("users"),
    provider: v.string(),
    status: v.union(v.literal("syncing"), v.literal("success"), v.literal("error"), v.literal("pending")),
    errorMessage: v.optional(v.string()),
    nextSyncAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const existingSync = await ctx.db
      .query("syncStatus")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("provider"), args.provider))
      .first();

    const now = Date.now();

    if (existingSync) {
      await ctx.db.patch(existingSync._id, {
        status: args.status,
        lastSyncAt: now,
        errorMessage: args.errorMessage,
        nextSyncAt: args.nextSyncAt,
        syncCount: existingSync.syncCount + 1,
      });
      return existingSync._id;
    } else {
      return await ctx.db.insert("syncStatus", {
        userId: args.userId,
        provider: args.provider,
        status: args.status,
        lastSyncAt: now,
        errorMessage: args.errorMessage,
        nextSyncAt: args.nextSyncAt,
        syncCount: 1,
      });
    }
  },
});

export const getSyncStatusByUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("syncStatus")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();
  },
});

export const getSyncStatusByProvider = query({
  args: { provider: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("syncStatus")
      .withIndex("by_provider", (q) => q.eq("provider", args.provider))
      .collect();
  },
});

export const getFailedSyncs = query({
  args: {},
  handler: async (ctx, args) => {
    return await ctx.db
      .query("syncStatus")
      .filter((q) => q.eq(q.field("status"), "error"))
      .collect();
  },
});

export const logActivity = mutation({
  args: {
    userId: v.id("users"),
    action: v.string(),
    entityType: v.string(),
    entityId: v.optional(v.string()),
    details: v.optional(v.any()),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("activityLog", {
      ...args,
      createdAt: Date.now(),
    });
  },
});

export const getActivityLog = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()),
    entityType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 100;
    
    let query = ctx.db
      .query("activityLog")
      .withIndex("by_user", (q) => q.eq("userId", args.userId));

    if (args.entityType) {
      query = query.filter((q) => q.eq(q.field("entityType"), args.entityType));
    }

    return await query
      .order("desc")
      .take(limit);
  },
});

// Realtime data subscription helpers
export const getRealtimeData = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // Get latest transactions
    const recentTransactions = await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(10);

    // Get account balances
    const accounts = await ctx.db
      .query("bankAccounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get budget progress
    const budgets = await ctx.db
      .query("budgets")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get unread notifications count
    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user_unread", (q) => 
        q.eq("userId", args.userId).eq("isRead", false)
      )
      .collect();

    // Get sync status
    const syncStatuses = await ctx.db
      .query("syncStatus")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    return {
      transactions: recentTransactions,
      accounts,
      budgets,
      unreadNotificationsCount: unreadNotifications.length,
      syncStatuses,
      lastUpdated: Date.now(),
    };
  },
});

export const triggerRealtimeUpdate = mutation({
  args: {
    userId: v.id("users"),
    updateType: v.string(),
    data: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    // Log the realtime update
    await ctx.db.insert("activityLog", {
      userId: args.userId,
      action: "realtime_update",
      entityType: args.updateType,
      details: args.data,
      createdAt: Date.now(),
    });

    // This function can be used to trigger specific realtime updates
    // The actual realtime functionality is handled by Convex's subscription system
    return { success: true, timestamp: Date.now() };
  },
});
