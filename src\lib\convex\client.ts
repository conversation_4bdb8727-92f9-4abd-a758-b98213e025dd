import { ConvexReactClient } from "convex/react";

if (!process.env.NEXT_PUBLIC_CONVEX_URL) {
  throw new Error("Missing NEXT_PUBLIC_CONVEX_URL environment variable");
}

// Create a single Convex client for the browser
export const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL, {
  // Disable the warning about unsaved changes
  unsavedChangesWarning: false,
});

// Export a type for type safety
export type ConvexClient = typeof convex;
