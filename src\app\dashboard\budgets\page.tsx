"use client";

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { BudgetCard } from '@/components/budgets/budget-card';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AIBudgetAnalyzer } from '@/lib/ai-budget';
import { Transaction } from '@/lib/types';
import { formatCurrency } from '@/lib/utils';
import { 
  Plus, 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Target,
  Lightbulb,
  Shield
} from 'lucide-react';

// Mock data for demonstration
const mockTransactions: Transaction[] = [
  {
    id: '1',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx1',
    amount: 450,
    currency: 'ZAR',
    description: 'Shoprite Groceries',
    category: 'groceries',
    date: Date.now() - ********,
    type: 'debit',
    merchant: 'Shoprite',
    isRecurring: false,
    tags: ['weekly'],
    createdAt: Date.now(),
  },
  {
    id: '2',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx2',
    amount: 15000,
    currency: 'ZAR',
    description: 'Salary Deposit',
    category: 'income',
    date: Date.now() - *********,
    type: 'credit',
    isRecurring: true,
    tags: ['monthly', 'salary'],
    createdAt: Date.now(),
  },
  // Add more mock transactions for better analysis
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `mock-${i}`,
    userId: 'user1',
    accountId: 'acc1',
    transactionId: `tx-mock-${i}`,
    amount: Math.random() * 1000 + 100,
    currency: 'ZAR',
    description: `Transaction ${i}`,
    category: ['groceries', 'transport', 'entertainment', 'utilities', 'dining'][Math.floor(Math.random() * 5)] as any,
    date: Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000, // Random date in last 90 days
    type: 'debit' as const,
    isRecurring: Math.random() > 0.7,
    tags: [],
    createdAt: Date.now(),
  }))
];

const mockBudgets = [
  {
    id: '1',
    userId: 'user1',
    name: 'Groceries',
    category: 'groceries' as const,
    amount: 2000,
    spent: 1650,
    period: 'monthly' as const,
    startDate: new Date(2024, 0, 1).getTime(),
    endDate: new Date(2024, 0, 31).getTime(),
    isActive: true,
    alerts: { enabled: true, threshold: 80 },
    createdAt: Date.now(),
    updatedAt: Date.now(),
    actualSpent: 1650,
    percentage: 82.5,
    isOverBudget: false,
    isNearLimit: true,
    remaining: 350,
  },
  {
    id: '2',
    userId: 'user1',
    name: 'Transport',
    category: 'transport' as const,
    amount: 1500,
    spent: 1750,
    period: 'monthly' as const,
    startDate: new Date(2024, 0, 1).getTime(),
    endDate: new Date(2024, 0, 31).getTime(),
    isActive: true,
    alerts: { enabled: true, threshold: 80 },
    createdAt: Date.now(),
    updatedAt: Date.now(),
    actualSpent: 1750,
    percentage: 116.7,
    isOverBudget: true,
    isNearLimit: false,
    remaining: 0,
  },
];

export default function BudgetsPage() {
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);

  const budgetAnalysis = useMemo(() => {
    return AIBudgetAnalyzer.analyzeBudget(mockTransactions, 3);
  }, []);

  const overBudgetCount = mockBudgets.filter(b => b.isOverBudget).length;
  const nearLimitCount = mockBudgets.filter(b => b.isNearLimit).length;
  const totalBudgeted = mockBudgets.reduce((sum, b) => sum + b.amount, 0);
  const totalSpent = mockBudgets.reduce((sum, b) => sum + b.actualSpent, 0);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Budgets</h1>
            <p className="text-gray-600">Track your spending and manage your budgets</p>
          </div>
          <div className="flex space-x-3">
            <Button 
              variant="outline"
              onClick={() => setShowAIAnalysis(!showAIAnalysis)}
            >
              <Brain className="w-4 h-4 mr-2" />
              AI Analysis
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Budget
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-blue-600" />
                </div>
                <span className="text-sm text-gray-500">Total Budgeted</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(totalBudgeted)}</div>
              <div className="text-sm text-gray-600">This month</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
                <span className="text-sm text-gray-500">Total Spent</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(totalSpent)}</div>
              <div className={`text-sm ${totalSpent > totalBudgeted ? 'text-red-600' : 'text-green-600'}`}>
                {((totalSpent / totalBudgeted) * 100).toFixed(1)}% of budget
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-orange-600" />
                </div>
                <span className="text-sm text-gray-500">Near Limit</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">{nearLimitCount}</div>
              <div className="text-sm text-orange-600">Budgets at 80%+</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <TrendingDown className="w-6 h-6 text-red-600" />
                </div>
                <span className="text-sm text-gray-500">Over Budget</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">{overBudgetCount}</div>
              <div className="text-sm text-red-600">Need attention</div>
            </CardContent>
          </Card>
        </div>

        {/* AI Analysis Panel */}
        {showAIAnalysis && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5 text-blue-600" />
                <span>AI Budget Analysis</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Financial Health Overview */}
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(budgetAnalysis.totalIncome)}
                  </div>
                  <div className="text-sm text-blue-700">Monthly Income</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {formatCurrency(budgetAnalysis.totalExpenses)}
                  </div>
                  <div className="text-sm text-red-700">Monthly Expenses</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {(budgetAnalysis.savingsRate * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-green-700">Savings Rate</div>
                </div>
              </div>

              {/* Insights */}
              {budgetAnalysis.insights.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Lightbulb className="w-4 h-4 mr-2 text-yellow-500" />
                    Key Insights
                  </h3>
                  <div className="space-y-2">
                    {budgetAnalysis.insights.map((insight, index) => (
                      <div key={index} className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <p className="text-sm text-yellow-800">{insight}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Risk Factors */}
              {budgetAnalysis.riskFactors.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Shield className="w-4 h-4 mr-2 text-red-500" />
                    Risk Factors
                  </h3>
                  <div className="space-y-2">
                    {budgetAnalysis.riskFactors.map((risk, index) => (
                      <div key={index} className="p-3 bg-red-50 rounded-lg border border-red-200">
                        <p className="text-sm text-red-800">{risk}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Top Recommendations */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Top Recommendations</h3>
                <div className="space-y-3">
                  {budgetAnalysis.recommendations
                    .filter(rec => rec.priority === 'high')
                    .slice(0, 3)
                    .map((rec, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Badge variant="destructive" className="capitalize">
                              {rec.category}
                            </Badge>
                            <span className="font-medium">
                              Suggested: {formatCurrency(rec.suggestedAmount)}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">
                            Current: {formatCurrency(rec.currentSpending)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{rec.reasoning}</p>
                        {rec.costCuttingTips.length > 0 && (
                          <div className="text-xs text-gray-500">
                            <strong>Tips:</strong> {rec.costCuttingTips[0]}
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Budget Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockBudgets.map((budget) => (
            <BudgetCard key={budget.id} budget={budget} />
          ))}
        </div>

        {/* Create Budget Suggestion */}
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-8 text-center">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Plus className="w-6 h-6 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Create Your First Budget</h3>
            <p className="text-gray-600 mb-4">
              Let our AI analyze your spending patterns and suggest personalized budgets
            </p>
            <Button>
              <Brain className="w-4 h-4 mr-2" />
              Generate AI Budget
            </Button>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
