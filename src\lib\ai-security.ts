import { Transaction } from './types';

export interface SecurityAlert {
  id: string;
  type: 'fraud_detection' | 'unusual_spending' | 'account_security' | 'data_breach' | 'suspicious_login';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  timestamp: number;
  resolved: boolean;
  actionRequired: boolean;
  recommendations: string[];
  affectedAccounts?: string[];
  transactionIds?: string[];
}

export interface FraudPattern {
  id: string;
  name: string;
  description: string;
  indicators: string[];
  riskScore: number;
  commonInSouthAfrica: boolean;
}

export interface SecurityScore {
  overall: number;
  breakdown: {
    passwordSecurity: number;
    accountActivity: number;
    transactionPatterns: number;
    deviceSecurity: number;
    personalInfo: number;
  };
  recommendations: string[];
}

export class AISecurityEngine {
  private static readonly FRAUD_PATTERNS: FraudPattern[] = [
    {
      id: 'card_skimming',
      name: 'Card Skimming',
      description: 'Unauthorized card data capture at ATMs or POS terminals',
      indicators: [
        'Multiple small transactions at different locations',
        'Transactions at unusual times (late night/early morning)',
        'Transactions in high-risk areas',
        'Rapid succession of transactions'
      ],
      riskScore: 8.5,
      commonInSouthAfrica: true
    },
    {
      id: 'sim_swap',
      name: 'SIM Swap Fraud',
      description: 'Fraudster transfers victim\'s phone number to their SIM card',
      indicators: [
        'Sudden loss of mobile signal',
        'Unauthorized banking app access',
        'OTP codes not received',
        'Unexpected account changes'
      ],
      riskScore: 9.2,
      commonInSouthAfrica: true
    },
    {
      id: 'phishing',
      name: 'Phishing Attack',
      description: 'Fraudulent attempts to obtain sensitive information',
      indicators: [
        'Login from unusual location',
        'Multiple failed login attempts',
        'Suspicious email clicks',
        'Unusual account access patterns'
      ],
      riskScore: 7.8,
      commonInSouthAfrica: true
    },
    {
      id: 'account_takeover',
      name: 'Account Takeover',
      description: 'Unauthorized access to user accounts',
      indicators: [
        'Password changes without user request',
        'New device registrations',
        'Unusual transaction patterns',
        'Contact information changes'
      ],
      riskScore: 9.0,
      commonInSouthAfrica: true
    },
    {
      id: 'social_engineering',
      name: 'Social Engineering',
      description: 'Manipulation to divulge confidential information',
      indicators: [
        'Unusual information requests',
        'Pressure to act quickly',
        'Requests for sensitive data',
        'Impersonation of trusted entities'
      ],
      riskScore: 8.0,
      commonInSouthAfrica: true
    }
  ];

  static analyzeTransactionSecurity(transactions: Transaction[]): SecurityAlert[] {
    const alerts: SecurityAlert[] = [];
    const now = Date.now();
    
    // Analyze for unusual spending patterns
    const unusualSpending = this.detectUnusualSpending(transactions);
    if (unusualSpending.length > 0) {
      alerts.push({
        id: `unusual-spending-${now}`,
        type: 'unusual_spending',
        severity: 'medium',
        title: 'Unusual Spending Pattern Detected',
        description: `We detected ${unusualSpending.length} transactions that deviate from your normal spending patterns.`,
        timestamp: now,
        resolved: false,
        actionRequired: true,
        recommendations: [
          'Review the flagged transactions carefully',
          'Contact your bank if you don\'t recognize any transactions',
          'Consider setting up transaction alerts',
          'Monitor your account more frequently'
        ],
        transactionIds: unusualSpending.map(t => t.id)
      });
    }

    // Check for rapid succession transactions (potential card skimming)
    const rapidTransactions = this.detectRapidTransactions(transactions);
    if (rapidTransactions.length > 0) {
      alerts.push({
        id: `rapid-transactions-${now}`,
        type: 'fraud_detection',
        severity: 'high',
        title: 'Rapid Transaction Pattern Detected',
        description: 'Multiple transactions in quick succession detected, which could indicate card skimming or unauthorized access.',
        timestamp: now,
        resolved: false,
        actionRequired: true,
        recommendations: [
          'Immediately check if you made these transactions',
          'Contact your bank to freeze your card if suspicious',
          'Change your banking passwords and PINs',
          'Monitor your account for additional unauthorized activity'
        ],
        transactionIds: rapidTransactions.map(t => t.id)
      });
    }

    // Check for off-hours transactions
    const offHoursTransactions = this.detectOffHoursTransactions(transactions);
    if (offHoursTransactions.length > 0) {
      alerts.push({
        id: `off-hours-${now}`,
        type: 'fraud_detection',
        severity: 'medium',
        title: 'Off-Hours Transaction Activity',
        description: 'Transactions detected during unusual hours (late night/early morning).',
        timestamp: now,
        resolved: false,
        actionRequired: false,
        recommendations: [
          'Verify these transactions were made by you',
          'Consider setting up time-based transaction limits',
          'Enable real-time transaction notifications'
        ],
        transactionIds: offHoursTransactions.map(t => t.id)
      });
    }

    return alerts;
  }

  static calculateSecurityScore(userProfile: {
    hasStrongPassword: boolean;
    hasTwoFactorAuth: boolean;
    lastPasswordChange: number;
    deviceCount: number;
    recentLoginLocations: string[];
    transactionHistory: Transaction[];
  }): SecurityScore {
    let passwordSecurity = 0;
    let accountActivity = 0;
    let transactionPatterns = 0;
    let deviceSecurity = 0;
    let personalInfo = 0;

    // Password Security (0-100)
    if (userProfile.hasStrongPassword) passwordSecurity += 40;
    if (userProfile.hasTwoFactorAuth) passwordSecurity += 40;
    const daysSincePasswordChange = (Date.now() - userProfile.lastPasswordChange) / (24 * 60 * 60 * 1000);
    if (daysSincePasswordChange < 90) passwordSecurity += 20;

    // Account Activity (0-100)
    if (userProfile.recentLoginLocations.length <= 2) accountActivity += 50;
    else if (userProfile.recentLoginLocations.length <= 5) accountActivity += 30;
    else accountActivity += 10;

    if (userProfile.deviceCount <= 3) accountActivity += 50;
    else if (userProfile.deviceCount <= 5) accountActivity += 30;
    else accountActivity += 10;

    // Transaction Patterns (0-100)
    const alerts = this.analyzeTransactionSecurity(userProfile.transactionHistory);
    const highSeverityAlerts = alerts.filter(a => a.severity === 'high' || a.severity === 'critical').length;
    
    if (highSeverityAlerts === 0) transactionPatterns = 100;
    else if (highSeverityAlerts <= 2) transactionPatterns = 70;
    else if (highSeverityAlerts <= 5) transactionPatterns = 40;
    else transactionPatterns = 20;

    // Device Security (0-100)
    deviceSecurity = Math.max(0, 100 - (userProfile.deviceCount * 10));

    // Personal Info (0-100)
    personalInfo = 80; // Base score, would be calculated based on data exposure

    const overall = Math.round(
      (passwordSecurity + accountActivity + transactionPatterns + deviceSecurity + personalInfo) / 5
    );

    const recommendations = this.generateSecurityRecommendations({
      passwordSecurity,
      accountActivity,
      transactionPatterns,
      deviceSecurity,
      personalInfo
    });

    return {
      overall,
      breakdown: {
        passwordSecurity,
        accountActivity,
        transactionPatterns,
        deviceSecurity,
        personalInfo
      },
      recommendations
    };
  }

  static generateSecurityTips(): string[] {
    return [
      'Never share your banking PINs or passwords with anyone',
      'Always log out of banking apps when finished',
      'Use strong, unique passwords for each account',
      'Enable two-factor authentication where available',
      'Regularly monitor your bank statements',
      'Be cautious of phishing emails and SMS messages',
      'Keep your banking app and phone software updated',
      'Use secure Wi-Fi networks for banking transactions',
      'Cover your PIN when entering it at ATMs',
      'Report suspicious activity immediately to your bank',
      'Never click links in suspicious emails claiming to be from your bank',
      'Use official banking apps downloaded from official app stores',
      'Set up transaction alerts for real-time monitoring',
      'Regularly review and update your contact information with your bank',
      'Be aware of your surroundings when using ATMs'
    ];
  }

  static getSouthAfricanFraudTrends(): Array<{
    trend: string;
    description: string;
    prevention: string[];
  }> {
    return [
      {
        trend: 'SIM Swap Attacks',
        description: 'Fraudsters transfer your phone number to their SIM card to intercept OTPs and access your accounts.',
        prevention: [
          'Contact your network provider to add extra security to your account',
          'Use app-based authentication instead of SMS where possible',
          'Be suspicious if you suddenly lose mobile signal',
          'Don\'t share personal information over the phone'
        ]
      },
      {
        trend: 'Card Skimming at ATMs',
        description: 'Devices attached to ATMs capture card data and PINs.',
        prevention: [
          'Inspect ATMs for unusual devices before use',
          'Cover your PIN when entering it',
          'Use ATMs inside banks when possible',
          'Check your statements regularly for unauthorized transactions'
        ]
      },
      {
        trend: 'WhatsApp Scams',
        description: 'Fraudsters impersonate family members or friends requesting money transfers.',
        prevention: [
          'Always verify requests through a phone call',
          'Be suspicious of urgent money requests',
          'Don\'t send money based on WhatsApp messages alone',
          'Educate family members about these scams'
        ]
      },
      {
        trend: 'Fake Banking Apps',
        description: 'Malicious apps designed to steal banking credentials.',
        prevention: [
          'Only download apps from official app stores',
          'Verify app developer information',
          'Check app reviews and ratings',
          'Never enter banking details in suspicious apps'
        ]
      }
    ];
  }

  private static detectUnusualSpending(transactions: Transaction[]): Transaction[] {
    const recentTransactions = transactions
      .filter(t => t.type === 'debit')
      .filter(t => Date.now() - t.date < 30 * 24 * 60 * 60 * 1000); // Last 30 days

    if (recentTransactions.length < 10) return [];

    // Calculate average transaction amount
    const amounts = recentTransactions.map(t => t.amount);
    const avgAmount = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
    const stdDev = Math.sqrt(
      amounts.reduce((sum, amount) => sum + Math.pow(amount - avgAmount, 2), 0) / amounts.length
    );

    // Flag transactions that are more than 2 standard deviations from the mean
    return recentTransactions.filter(t => Math.abs(t.amount - avgAmount) > 2 * stdDev);
  }

  private static detectRapidTransactions(transactions: Transaction[]): Transaction[] {
    const sortedTransactions = transactions
      .filter(t => t.type === 'debit')
      .sort((a, b) => a.date - b.date);

    const rapidTransactions: Transaction[] = [];
    
    for (let i = 1; i < sortedTransactions.length; i++) {
      const timeDiff = sortedTransactions[i].date - sortedTransactions[i - 1].date;
      const fiveMinutes = 5 * 60 * 1000;
      
      if (timeDiff < fiveMinutes) {
        rapidTransactions.push(sortedTransactions[i - 1], sortedTransactions[i]);
      }
    }

    return [...new Set(rapidTransactions)]; // Remove duplicates
  }

  private static detectOffHoursTransactions(transactions: Transaction[]): Transaction[] {
    return transactions
      .filter(t => t.type === 'debit')
      .filter(t => {
        const hour = new Date(t.date).getHours();
        return hour < 6 || hour > 22; // Before 6 AM or after 10 PM
      });
  }

  private static generateSecurityRecommendations(scores: {
    passwordSecurity: number;
    accountActivity: number;
    transactionPatterns: number;
    deviceSecurity: number;
    personalInfo: number;
  }): string[] {
    const recommendations: string[] = [];

    if (scores.passwordSecurity < 80) {
      recommendations.push('Enable two-factor authentication on all accounts');
      recommendations.push('Update your passwords to be stronger and unique');
      recommendations.push('Consider using a password manager');
    }

    if (scores.accountActivity < 70) {
      recommendations.push('Review and remove unused devices from your accounts');
      recommendations.push('Monitor login locations and report suspicious activity');
      recommendations.push('Set up login alerts for new devices');
    }

    if (scores.transactionPatterns < 80) {
      recommendations.push('Review recent transactions for any unauthorized activity');
      recommendations.push('Set up real-time transaction alerts');
      recommendations.push('Contact your bank about suspicious patterns');
    }

    if (scores.deviceSecurity < 70) {
      recommendations.push('Limit the number of devices with access to your accounts');
      recommendations.push('Regularly review connected devices');
      recommendations.push('Remove access for devices you no longer use');
    }

    if (scores.personalInfo < 80) {
      recommendations.push('Review privacy settings on social media');
      recommendations.push('Be cautious about sharing personal information online');
      recommendations.push('Monitor for data breaches affecting your accounts');
    }

    return recommendations;
  }

  static getFraudPatterns(): FraudPattern[] {
    return this.FRAUD_PATTERNS;
  }

  static getPatternById(id: string): FraudPattern | undefined {
    return this.FRAUD_PATTERNS.find(pattern => pattern.id === id);
  }

  static generateSecurityReport(
    transactions: Transaction[],
    userProfile: any
  ): {
    securityScore: SecurityScore;
    alerts: SecurityAlert[];
    fraudPatterns: FraudPattern[];
    recommendations: string[];
  } {
    const securityScore = this.calculateSecurityScore({
      hasStrongPassword: userProfile.hasStrongPassword || false,
      hasTwoFactorAuth: userProfile.hasTwoFactorAuth || false,
      lastPasswordChange: userProfile.lastPasswordChange || Date.now() - 180 * 24 * 60 * 60 * 1000,
      deviceCount: userProfile.deviceCount || 2,
      recentLoginLocations: userProfile.recentLoginLocations || ['Johannesburg', 'Cape Town'],
      transactionHistory: transactions
    });

    const alerts = this.analyzeTransactionSecurity(transactions);
    const fraudPatterns = this.getFraudPatterns().filter(p => p.commonInSouthAfrica);
    const recommendations = this.generateSecurityTips();

    return {
      securityScore,
      alerts,
      fraudPatterns,
      recommendations
    };
  }
}
