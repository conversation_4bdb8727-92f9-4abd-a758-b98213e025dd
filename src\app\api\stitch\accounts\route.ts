import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { stitchAPI, categorizeTransaction } from '@/lib/stitch';

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const userToken = searchParams.get('token');
    
    if (!userToken) {
      return NextResponse.json({ error: 'User token required' }, { status: 400 });
    }

    const accounts = await stitchAPI.getAccounts(userToken);
    
    return NextResponse.json({ accounts });
  } catch (error) {
    console.error('Stitch accounts error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch accounts' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userToken, accountId } = await request.json();
    
    if (!userToken || !accountId) {
      return NextResponse.json({ error: 'User token and account ID required' }, { status: 400 });
    }

    // Fetch transactions for the account
    const transactions = await stitchAPI.getTransactions(userToken, accountId, 100);
    
    // Categorize transactions
    const categorizedTransactions = transactions.map(transaction => ({
      ...transaction,
      category: categorizeTransaction(transaction.description, transaction.merchant),
    }));
    
    return NextResponse.json({ transactions: categorizedTransactions });
  } catch (error) {
    console.error('Stitch transactions error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch transactions' },
      { status: 500 }
    );
  }
}
