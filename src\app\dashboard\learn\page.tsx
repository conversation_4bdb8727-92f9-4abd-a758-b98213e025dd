"use client";

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { AIEducationEngine, EducationalContent } from '@/lib/ai-education';
import { Transaction } from '@/lib/types';
import { 
  BookOpen, 
  Brain, 
  MessageCircle,
  Search,
  Clock,
  Target,
  CheckCircle,
  Star,
  TrendingUp,
  Lightbulb,
  Users,
  Award,
  Play,
  ArrowRight
} from 'lucide-react';

// Mock user data
const mockUserProfile = {
  experience: 'beginner' as const,
  interests: ['saving', 'budgeting'],
  financialGoals: ['save_money', 'emergency_fund']
};

const mockTransactions: Transaction[] = [
  {
    id: '1',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx1',
    amount: 15000,
    currency: 'ZAR',
    description: 'Salary Deposit',
    category: 'income',
    date: Date.now() - *********,
    type: 'credit',
    isRecurring: true,
    tags: ['monthly', 'salary'],
    createdAt: Date.now(),
  },
  // Add more mock data for better recommendations
  ...Array.from({ length: 15 }, (_, i) => ({
    id: `mock-${i}`,
    userId: 'user1',
    accountId: 'acc1',
    transactionId: `tx-mock-${i}`,
    amount: Math.random() * 1000 + 50,
    currency: 'ZAR',
    description: `Transaction ${i}`,
    category: ['groceries', 'transport', 'entertainment', 'utilities', 'dining'][Math.floor(Math.random() * 5)] as any,
    date: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
    type: 'debit' as const,
    isRecurring: Math.random() > 0.8,
    tags: [],
    createdAt: Date.now(),
  }))
];

export default function LearnPage() {
  const [selectedContent, setSelectedContent] = useState<EducationalContent | null>(null);
  const [chatInput, setChatInput] = useState('');
  const [chatHistory, setChatHistory] = useState<Array<{
    type: 'user' | 'bot';
    message: string;
    suggestions?: string[];
  }>>([
    {
      type: 'bot',
      message: "Hi! I'm your AI financial education assistant. I can help you learn about South African banking, saving, investing, and more. What would you like to learn about today?",
      suggestions: [
        "How do I start budgeting?",
        "Tell me about South African banks",
        "How much should I save for emergencies?",
        "What are Tax-Free Savings Accounts?"
      ]
    }
  ]);
  const [searchQuery, setSearchQuery] = useState('');

  // Get personalized content recommendations
  const personalizedContent = useMemo(() => {
    return AIEducationEngine.getPersonalizedContent(mockUserProfile, mockTransactions);
  }, []);

  // Get learning path
  const learningPath = useMemo(() => {
    return AIEducationEngine.generateLearningPath(mockUserProfile.financialGoals, mockUserProfile.experience);
  }, []);

  // Get all content for browsing
  const allContent = useMemo(() => {
    return AIEducationEngine.getAllContent();
  }, []);

  // Filter content based on search
  const filteredContent = useMemo(() => {
    if (!searchQuery) return allContent;
    return AIEducationEngine.searchContent(searchQuery);
  }, [searchQuery, allContent]);

  const handleChatSubmit = () => {
    if (!chatInput.trim()) return;

    // Add user message
    const newHistory = [...chatHistory, { type: 'user' as const, message: chatInput }];
    
    // Get AI response
    const response = AIEducationEngine.chatbotResponse(chatInput, {
      transactions: mockTransactions,
      goals: mockUserProfile.financialGoals,
      experience: mockUserProfile.experience
    });

    // Add bot response
    newHistory.push({
      type: 'bot',
      message: response.message,
      suggestions: response.suggestions
    });

    setChatHistory(newHistory);
    setChatInput('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    setChatInput(suggestion);
    handleChatSubmit();
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-600 bg-green-100';
      case 'intermediate': return 'text-yellow-600 bg-yellow-100';
      case 'advanced': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'banking': return <Target className="w-4 h-4" />;
      case 'saving': return <TrendingUp className="w-4 h-4" />;
      case 'investing': return <Star className="w-4 h-4" />;
      case 'budgeting': return <BookOpen className="w-4 h-4" />;
      case 'taxes': return <Award className="w-4 h-4" />;
      default: return <Lightbulb className="w-4 h-4" />;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Financial Education</h1>
            <p className="text-gray-600">Learn, grow, and master your finances with AI-powered guidance</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Users className="w-4 h-4 mr-2" />
              Community
            </Button>
            <Button>
              <MessageCircle className="w-4 h-4 mr-2" />
              Ask AI
            </Button>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="grid md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
                <Badge variant="outline" className="text-blue-600">
                  Beginner
                </Badge>
              </div>
              <div className="text-2xl font-bold text-gray-900">3</div>
              <div className="text-sm text-gray-600">Modules Completed</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-green-600" />
                </div>
                <Badge variant="outline" className="text-green-600">
                  2.5 hrs
                </Badge>
              </div>
              <div className="text-2xl font-bold text-gray-900">45</div>
              <div className="text-sm text-gray-600">Minutes This Week</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-purple-600" />
                </div>
                <Badge variant="outline" className="text-purple-600">
                  On Track
                </Badge>
              </div>
              <div className="text-2xl font-bold text-gray-900">75%</div>
              <div className="text-sm text-gray-600">Learning Path Progress</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Award className="w-6 h-6 text-orange-600" />
                </div>
                <Badge variant="outline" className="text-orange-600">
                  New!
                </Badge>
              </div>
              <div className="text-2xl font-bold text-gray-900">2</div>
              <div className="text-sm text-gray-600">Achievements Earned</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Learning Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Personalized Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="w-5 h-5 text-blue-600" />
                  <span>Recommended for You</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {personalizedContent.slice(0, 3).map((content) => (
                    <div 
                      key={content.id} 
                      className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => setSelectedContent(content)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getCategoryIcon(content.category)}
                          <h3 className="font-semibold text-gray-900">{content.title}</h3>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getDifficultyColor(content.difficulty)}>
                            {content.difficulty}
                          </Badge>
                          <Badge variant="outline">
                            <Clock className="w-3 h-3 mr-1" />
                            {content.estimatedTime}m
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{content.description}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {content.southAfricanContext && (
                            <Badge variant="outline" className="text-green-600">
                              🇿🇦 SA Context
                            </Badge>
                          )}
                        </div>
                        <Button variant="outline" size="sm">
                          <Play className="w-3 h-3 mr-1" />
                          Start Learning
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Learning Path */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Your Learning Path</span>
                  <Badge variant="outline">{learningPath.estimatedDuration}h total</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Progress</span>
                    <span>75% complete</span>
                  </div>
                  <Progress value={75} className="mb-4" />
                </div>
                
                <div className="space-y-3">
                  {learningPath.modules
                    .filter((module, index, self) => 
                      index === self.findIndex(m => m.id === module.id)
                    )
                    .slice(0, 4)
                    .map((module, index) => (
                    <div key={module.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        index < 3 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                      }`}>
                        {index < 3 ? <CheckCircle className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full" />}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{module.title}</div>
                        <div className="text-sm text-gray-600">{module.estimatedTime} minutes</div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <ArrowRight className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Browse All Content */}
            <Card>
              <CardHeader>
                <CardTitle>Browse All Content</CardTitle>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search financial topics..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {filteredContent.slice(0, 6).map((content) => (
                    <div 
                      key={content.id}
                      className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => setSelectedContent(content)}
                    >
                      <div className="flex items-center space-x-2 mb-2">
                        {getCategoryIcon(content.category)}
                        <span className="font-medium text-gray-900 text-sm">{content.title}</span>
                      </div>
                      <p className="text-xs text-gray-600 mb-2">{content.description}</p>
                      <div className="flex items-center justify-between">
                        <Badge className={getDifficultyColor(content.difficulty)} size="sm">
                          {content.difficulty}
                        </Badge>
                        <span className="text-xs text-gray-500">{content.estimatedTime}m</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* AI Chatbot */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageCircle className="w-5 h-5 text-blue-600" />
                <span>AI Financial Assistant</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Chat History */}
              <div className="h-96 overflow-y-auto space-y-3 p-3 bg-gray-50 rounded-lg">
                {chatHistory.map((message, index) => (
                  <div key={index} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs p-3 rounded-lg ${
                      message.type === 'user' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-white border'
                    }`}>
                      <p className="text-sm">{message.message}</p>
                      {message.suggestions && (
                        <div className="mt-2 space-y-1">
                          {message.suggestions.map((suggestion, idx) => (
                            <button
                              key={idx}
                              onClick={() => handleSuggestionClick(suggestion)}
                              className="block w-full text-left text-xs p-2 bg-blue-50 hover:bg-blue-100 rounded border text-blue-700"
                            >
                              {suggestion}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Chat Input */}
              <div className="flex space-x-2">
                <Input
                  placeholder="Ask me anything about finance..."
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleChatSubmit()}
                />
                <Button onClick={handleChatSubmit}>
                  <MessageCircle className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content Modal */}
        {selectedContent && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-bold text-gray-900">{selectedContent.title}</h2>
                  <Button variant="ghost" onClick={() => setSelectedContent(null)}>
                    ✕
                  </Button>
                </div>
                
                <div className="flex items-center space-x-4 mb-6">
                  <Badge className={getDifficultyColor(selectedContent.difficulty)}>
                    {selectedContent.difficulty}
                  </Badge>
                  <Badge variant="outline">
                    <Clock className="w-3 h-3 mr-1" />
                    {selectedContent.estimatedTime} minutes
                  </Badge>
                  {selectedContent.southAfricanContext && (
                    <Badge variant="outline" className="text-green-600">
                      🇿🇦 South African Context
                    </Badge>
                  )}
                </div>

                <div className="prose max-w-none">
                  <div className="whitespace-pre-line text-gray-700 mb-6">
                    {selectedContent.content}
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">Key Takeaways</h3>
                      <ul className="space-y-2">
                        {selectedContent.keyTakeaways.map((takeaway, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                            <span className="text-sm text-gray-700">{takeaway}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">Action Items</h3>
                      <ul className="space-y-2">
                        {selectedContent.actionItems.map((action, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <Target className="w-4 h-4 text-blue-600 mt-0.5" />
                            <span className="text-sm text-gray-700">{action}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
                  <Button variant="outline" onClick={() => setSelectedContent(null)}>
                    Close
                  </Button>
                  <Button>
                    Mark as Complete
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
