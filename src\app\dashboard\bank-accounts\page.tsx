'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { BankAccountsList } from '@/components/bank-accounts/BankAccountsList';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function BankAccountsPage() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const router = useRouter();

  if (!isUserLoaded) {
    return (
      <div className="container mx-auto py-6">
        <div className="animate-pulse space-y-4">
          <div className="h-10 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-100 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push('/sign-in');
    return null;
  }

  const handleAddAccount = () => {
    // TODO: Implement add account flow
    console.log('Add account clicked');
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Bank Accounts</h1>
          <p className="text-muted-foreground">Manage your connected bank accounts</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="icon"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
          <Button onClick={handleAddAccount}>
            <Plus className="mr-2 h-4 w-4" />
            Add Account
          </Button>
        </div>
      </div>
      
      <Card>
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <CardTitle>Your Accounts</CardTitle>
            <div className="text-sm text-muted-foreground">
              {isRefreshing ? 'Refreshing...' : 'Updated just now'}
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <BankAccountsList />
        </CardContent>
      </Card>
    </div>
  );
}
