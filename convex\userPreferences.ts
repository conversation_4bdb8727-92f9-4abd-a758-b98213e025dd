import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const getUserPreferences = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const preferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // Return default preferences if none exist
    if (!preferences) {
      return {
        notifications: {
          budgetAlerts: true,
          transactionAlerts: true,
          savingsReminders: true,
          billReminders: true,
          securityAlerts: true,
          aiInsights: true,
          stokvelUpdates: true,
          emailNotifications: true,
          pushNotifications: true,
        },
        privacy: {
          shareDataForAI: true,
          allowAnalytics: true,
          shareWithStokvel: true,
        },
        display: {
          currency: "ZAR",
          dateFormat: "DD/MM/YYYY",
          theme: "light" as const,
          language: "en",
        },
        updatedAt: Date.now(),
      };
    }

    return preferences;
  },
});

export const updateUserPreferences = mutation({
  args: {
    userId: v.id("users"),
    notifications: v.optional(v.object({
      budgetAlerts: v.optional(v.boolean()),
      transactionAlerts: v.optional(v.boolean()),
      savingsReminders: v.optional(v.boolean()),
      billReminders: v.optional(v.boolean()),
      securityAlerts: v.optional(v.boolean()),
      aiInsights: v.optional(v.boolean()),
      stokvelUpdates: v.optional(v.boolean()),
      emailNotifications: v.optional(v.boolean()),
      pushNotifications: v.optional(v.boolean()),
    })),
    privacy: v.optional(v.object({
      shareDataForAI: v.optional(v.boolean()),
      allowAnalytics: v.optional(v.boolean()),
      shareWithStokvel: v.optional(v.boolean()),
    })),
    display: v.optional(v.object({
      currency: v.optional(v.string()),
      dateFormat: v.optional(v.string()),
      theme: v.optional(v.union(v.literal("light"), v.literal("dark"), v.literal("auto"))),
      language: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;
    
    const existingPreferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    const now = Date.now();

    if (existingPreferences) {
      // Update existing preferences
      const updatedData: any = { updatedAt: now };
      
      if (updates.notifications) {
        updatedData.notifications = {
          ...existingPreferences.notifications,
          ...updates.notifications,
        };
      }
      
      if (updates.privacy) {
        updatedData.privacy = {
          ...existingPreferences.privacy,
          ...updates.privacy,
        };
      }
      
      if (updates.display) {
        updatedData.display = {
          ...existingPreferences.display,
          ...updates.display,
        };
      }

      await ctx.db.patch(existingPreferences._id, updatedData);
      return existingPreferences._id;
    } else {
      // Create new preferences with defaults
      const defaultPreferences = {
        userId,
        notifications: {
          budgetAlerts: true,
          transactionAlerts: true,
          savingsReminders: true,
          billReminders: true,
          securityAlerts: true,
          aiInsights: true,
          stokvelUpdates: true,
          emailNotifications: true,
          pushNotifications: true,
          ...updates.notifications,
        },
        privacy: {
          shareDataForAI: true,
          allowAnalytics: true,
          shareWithStokvel: true,
          ...updates.privacy,
        },
        display: {
          currency: "ZAR",
          dateFormat: "DD/MM/YYYY",
          theme: "light" as const,
          language: "en",
          ...updates.display,
        },
        updatedAt: now,
      };

      return await ctx.db.insert("userPreferences", defaultPreferences);
    }
  },
});

export const createOrUpdateUser = mutation({
  args: {
    clerkId: v.string(),
    email: v.string(),
    firstName: v.string(),
    lastName: v.string(),
  },
  handler: async (ctx, args) => {
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();

    const now = Date.now();

    if (existingUser) {
      // Update existing user
      await ctx.db.patch(existingUser._id, {
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        updatedAt: now,
      });
      return existingUser._id;
    } else {
      // Create new user
      const userId = await ctx.db.insert("users", {
        clerkId: args.clerkId,
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        createdAt: now,
        updatedAt: now,
      });

      // Create default preferences for new user
      await ctx.db.insert("userPreferences", {
        userId,
        notifications: {
          budgetAlerts: true,
          transactionAlerts: true,
          savingsReminders: true,
          billReminders: true,
          securityAlerts: true,
          aiInsights: true,
          stokvelUpdates: true,
          emailNotifications: true,
          pushNotifications: true,
        },
        privacy: {
          shareDataForAI: true,
          allowAnalytics: true,
          shareWithStokvel: true,
        },
        display: {
          currency: "ZAR",
          dateFormat: "DD/MM/YYYY",
          theme: "light" as const,
          language: "en",
        },
        updatedAt: now,
      });

      return userId;
    }
  },
});

export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();
  },
});
