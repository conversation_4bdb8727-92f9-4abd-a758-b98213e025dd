import { Transaction, SavingsGoal, TransactionCategory } from './types';
import { TransactionCategorizer } from './ai-categorization';

export interface SavingsRecommendation {
  id: string;
  type: 'round_up' | 'recurring' | 'goal_based' | 'expense_reduction' | 'income_boost';
  title: string;
  description: string;
  potentialSavings: number;
  timeframe: 'daily' | 'weekly' | 'monthly' | 'yearly';
  confidence: number;
  priority: 'high' | 'medium' | 'low';
  actionSteps: string[];
  category?: TransactionCategory;
  automatable: boolean;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface SavingsAnalysis {
  currentSavingsRate: number;
  recommendedSavingsRate: number;
  monthlySavingsPotential: number;
  recommendations: SavingsRecommendation[];
  insights: string[];
  emergencyFundStatus: {
    current: number;
    recommended: number;
    monthsOfExpenses: number;
  };
}

export class AISavingsEngine {
  private static readonly EMERGENCY_FUND_MONTHS = 6;
  private static readonly RECOMMENDED_SAVINGS_RATE = 0.2; // 20%

  static analyzeSavings(
    transactions: Transaction[],
    savingsGoals: SavingsGoal[] = [],
    monthlyIncome: number,
    currentSavings: number = 0
  ): SavingsAnalysis {
    const monthlyExpenses = this.calculateMonthlyExpenses(transactions);
    const currentSavingsRate = monthlyIncome > 0 ? (monthlyIncome - monthlyExpenses) / monthlyIncome : 0;
    
    const recommendations = this.generateSavingsRecommendations(
      transactions,
      savingsGoals,
      monthlyIncome,
      monthlyExpenses
    );
    
    const monthlySavingsPotential = recommendations.reduce(
      (total, rec) => total + this.convertToMonthly(rec.potentialSavings, rec.timeframe),
      0
    );
    
    const insights = this.generateSavingsInsights(
      transactions,
      currentSavingsRate,
      monthlyIncome,
      monthlyExpenses
    );
    
    const emergencyFundRecommended = monthlyExpenses * this.EMERGENCY_FUND_MONTHS;
    const emergencyFundStatus = {
      current: currentSavings,
      recommended: emergencyFundRecommended,
      monthsOfExpenses: monthlyExpenses > 0 ? currentSavings / monthlyExpenses : 0
    };
    
    return {
      currentSavingsRate,
      recommendedSavingsRate: this.RECOMMENDED_SAVINGS_RATE,
      monthlySavingsPotential,
      recommendations: recommendations.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }),
      insights,
      emergencyFundStatus
    };
  }

  private static calculateMonthlyExpenses(transactions: Transaction[]): number {
    const expenseTransactions = transactions.filter(t => t.type === 'debit');
    const totalExpenses = expenseTransactions.reduce((sum, t) => sum + t.amount, 0);
    const months = Math.max(1, this.getTransactionTimespan(transactions) / (30 * 24 * 60 * 60 * 1000));
    return totalExpenses / months;
  }

  private static getTransactionTimespan(transactions: Transaction[]): number {
    if (transactions.length === 0) return 30 * 24 * 60 * 60 * 1000; // Default to 30 days
    
    const dates = transactions.map(t => t.date).sort();
    return dates[dates.length - 1] - dates[0];
  }

  private static generateSavingsRecommendations(
    transactions: Transaction[],
    savingsGoals: SavingsGoal[],
    monthlyIncome: number,
    monthlyExpenses: number
  ): SavingsRecommendation[] {
    const recommendations: SavingsRecommendation[] = [];
    
    // Round-up savings
    recommendations.push(...this.generateRoundUpRecommendations(transactions));
    
    // Recurring savings based on income
    recommendations.push(...this.generateRecurringSavingsRecommendations(monthlyIncome));
    
    // Goal-based recommendations
    recommendations.push(...this.generateGoalBasedRecommendations(savingsGoals, monthlyIncome));
    
    // Expense reduction recommendations
    recommendations.push(...this.generateExpenseReductionRecommendations(transactions));
    
    // Income boost recommendations
    recommendations.push(...this.generateIncomeBoostRecommendations(transactions, monthlyIncome));
    
    return recommendations;
  }

  private static generateRoundUpRecommendations(transactions: Transaction[]): SavingsRecommendation[] {
    const debitTransactions = transactions.filter(t => t.type === 'debit');
    const totalRoundUp = debitTransactions.reduce((sum, t) => {
      const roundedAmount = Math.ceil(t.amount);
      return sum + (roundedAmount - t.amount);
    }, 0);
    
    const months = Math.max(1, this.getTransactionTimespan(transactions) / (30 * 24 * 60 * 60 * 1000));
    const monthlyRoundUp = totalRoundUp / months;
    
    if (monthlyRoundUp > 10) {
      return [{
        id: 'round-up-savings',
        type: 'round_up',
        title: 'Round-Up Savings',
        description: 'Automatically round up your purchases to the nearest rand and save the difference.',
        potentialSavings: monthlyRoundUp,
        timeframe: 'monthly',
        confidence: 0.9,
        priority: 'high',
        actionSteps: [
          'Enable round-up savings in your banking app',
          'Set up automatic transfers to savings account',
          'Monitor your round-up savings monthly'
        ],
        automatable: true,
        riskLevel: 'low'
      }];
    }
    
    return [];
  }

  private static generateRecurringSavingsRecommendations(monthlyIncome: number): SavingsRecommendation[] {
    const recommendations: SavingsRecommendation[] = [];
    
    // 50/30/20 rule recommendation
    const recommendedSavings = monthlyIncome * 0.2;
    
    recommendations.push({
      id: 'recurring-savings-20',
      type: 'recurring',
      title: '20% Savings Rule',
      description: 'Save 20% of your income automatically each month following the 50/30/20 budgeting rule.',
      potentialSavings: recommendedSavings,
      timeframe: 'monthly',
      confidence: 0.8,
      priority: 'high',
      actionSteps: [
        'Set up automatic transfer on payday',
        'Open a separate high-yield savings account',
        'Start with 10% and gradually increase to 20%',
        'Review and adjust monthly'
      ],
      automatable: true,
      riskLevel: 'low'
    });
    
    // Pay yourself first
    const payYourselfFirst = monthlyIncome * 0.1;
    
    recommendations.push({
      id: 'pay-yourself-first',
      type: 'recurring',
      title: 'Pay Yourself First',
      description: 'Automatically save 10% of your income before any expenses.',
      potentialSavings: payYourselfFirst,
      timeframe: 'monthly',
      confidence: 0.85,
      priority: 'medium',
      actionSteps: [
        'Set up automatic transfer immediately after salary deposit',
        'Treat savings as a non-negotiable expense',
        'Use a separate savings account'
      ],
      automatable: true,
      riskLevel: 'low'
    });
    
    return recommendations;
  }

  private static generateGoalBasedRecommendations(
    savingsGoals: SavingsGoal[],
    monthlyIncome: number
  ): SavingsRecommendation[] {
    const recommendations: SavingsRecommendation[] = [];
    
    savingsGoals.forEach(goal => {
      const monthsRemaining = Math.max(1, (goal.targetDate - Date.now()) / (30 * 24 * 60 * 60 * 1000));
      const monthlyRequired = (goal.targetAmount - goal.currentAmount) / monthsRemaining;
      
      if (monthlyRequired > 0 && monthlyRequired < monthlyIncome * 0.3) {
        recommendations.push({
          id: `goal-${goal.id}`,
          type: 'goal_based',
          title: `Save for ${goal.name}`,
          description: `Automatically save R${monthlyRequired.toFixed(2)} monthly to reach your ${goal.name} goal.`,
          potentialSavings: monthlyRequired,
          timeframe: 'monthly',
          confidence: 0.9,
          priority: goal.priority === 'high' ? 'high' : 'medium',
          actionSteps: [
            'Set up dedicated savings account for this goal',
            'Automate monthly transfers',
            'Track progress weekly',
            'Adjust if income changes'
          ],
          automatable: true,
          riskLevel: 'low'
        });
      }
    });
    
    return recommendations;
  }

  private static generateExpenseReductionRecommendations(transactions: Transaction[]): SavingsRecommendation[] {
    const recommendations: SavingsRecommendation[] = [];
    const analysis = TransactionCategorizer.analyzeSpendingPatterns(transactions);
    
    // Subscription audit
    const subscriptions = transactions.filter(t => 
      t.isRecurring && 
      ['entertainment', 'shopping'].includes(t.category) &&
      t.amount < 500
    );
    
    if (subscriptions.length > 0) {
      const totalSubscriptions = subscriptions.reduce((sum, t) => sum + t.amount, 0);
      const months = Math.max(1, this.getTransactionTimespan(transactions) / (30 * 24 * 60 * 60 * 1000));
      const monthlySubscriptions = totalSubscriptions / months;
      
      recommendations.push({
        id: 'subscription-audit',
        type: 'expense_reduction',
        title: 'Subscription Audit',
        description: `Review and cancel unused subscriptions. You could save up to R${(monthlySubscriptions * 0.3).toFixed(2)} monthly.`,
        potentialSavings: monthlySubscriptions * 0.3,
        timeframe: 'monthly',
        confidence: 0.7,
        priority: 'medium',
        actionSteps: [
          'List all recurring subscriptions',
          'Cancel unused or duplicate services',
          'Negotiate better rates with providers',
          'Use family plans where possible'
        ],
        category: 'entertainment',
        automatable: false,
        riskLevel: 'low'
      });
    }
    
    // Dining out reduction
    const diningExpenses = analysis.categoryBreakdown.dining || 0;
    const months = Math.max(1, this.getTransactionTimespan(transactions) / (30 * 24 * 60 * 60 * 1000));
    const monthlyDining = diningExpenses / months;
    
    if (monthlyDining > 500) {
      recommendations.push({
        id: 'reduce-dining-out',
        type: 'expense_reduction',
        title: 'Cook More at Home',
        description: `Reduce dining out by 50% and save approximately R${(monthlyDining * 0.5).toFixed(2)} monthly.`,
        potentialSavings: monthlyDining * 0.5,
        timeframe: 'monthly',
        confidence: 0.6,
        priority: 'medium',
        actionSteps: [
          'Plan meals for the week',
          'Batch cook on weekends',
          'Pack lunch for work',
          'Limit restaurant visits to special occasions'
        ],
        category: 'dining',
        automatable: false,
        riskLevel: 'low'
      });
    }
    
    return recommendations;
  }

  private static generateIncomeBoostRecommendations(
    transactions: Transaction[],
    monthlyIncome: number
  ): SavingsRecommendation[] {
    const recommendations: SavingsRecommendation[] = [];
    
    // Side hustle recommendation
    const potentialSideIncome = monthlyIncome * 0.2; // 20% additional income
    
    recommendations.push({
      id: 'side-hustle',
      type: 'income_boost',
      title: 'Start a Side Hustle',
      description: 'Explore freelancing or part-time work to increase your income by 20%.',
      potentialSavings: potentialSideIncome,
      timeframe: 'monthly',
      confidence: 0.5,
      priority: 'low',
      actionSteps: [
        'Identify your skills and interests',
        'Research online platforms (Upwork, Fiverr)',
        'Start with small projects',
        'Gradually build your client base'
      ],
      automatable: false,
      riskLevel: 'medium'
    });
    
    // Cashback and rewards
    const monthlySpending = transactions
      .filter(t => t.type === 'debit')
      .reduce((sum, t) => sum + t.amount, 0) / 
      Math.max(1, this.getTransactionTimespan(transactions) / (30 * 24 * 60 * 60 * 1000));
    
    const cashbackPotential = monthlySpending * 0.02; // 2% cashback
    
    if (cashbackPotential > 50) {
      recommendations.push({
        id: 'cashback-rewards',
        type: 'income_boost',
        title: 'Maximize Cashback Rewards',
        description: `Earn up to R${cashbackPotential.toFixed(2)} monthly through cashback credit cards and reward programs.`,
        potentialSavings: cashbackPotential,
        timeframe: 'monthly',
        confidence: 0.8,
        priority: 'medium',
        actionSteps: [
          'Apply for cashback credit cards',
          'Use reward programs at grocery stores',
          'Pay off credit card balances monthly',
          'Track and redeem rewards regularly'
        ],
        automatable: false,
        riskLevel: 'low'
      });
    }
    
    return recommendations;
  }

  private static generateSavingsInsights(
    transactions: Transaction[],
    currentSavingsRate: number,
    monthlyIncome: number,
    monthlyExpenses: number
  ): string[] {
    const insights: string[] = [];
    
    if (currentSavingsRate < 0.1) {
      insights.push('Your savings rate is below 10%. Focus on building an emergency fund first.');
    } else if (currentSavingsRate > 0.25) {
      insights.push('Excellent savings rate! Consider investing excess savings for long-term growth.');
    }
    
    const expenseToIncomeRatio = monthlyIncome > 0 ? monthlyExpenses / monthlyIncome : 0;
    if (expenseToIncomeRatio > 0.8) {
      insights.push('Your expenses are over 80% of income. Look for ways to reduce costs or increase income.');
    }
    
    // Analyze spending volatility
    const monthlySpending = this.getMonthlySpendingPattern(transactions);
    if (monthlySpending.length > 1) {
      const avgSpending = monthlySpending.reduce((sum, amount) => sum + amount, 0) / monthlySpending.length;
      const variance = monthlySpending.reduce((sum, amount) => sum + Math.pow(amount - avgSpending, 2), 0) / monthlySpending.length;
      const volatility = Math.sqrt(variance) / avgSpending;
      
      if (volatility > 0.3) {
        insights.push('Your spending varies significantly month-to-month. Consider creating a more consistent budget.');
      }
    }
    
    return insights;
  }

  private static getMonthlySpendingPattern(transactions: Transaction[]): number[] {
    const monthlySpending: Record<string, number> = {};
    
    transactions
      .filter(t => t.type === 'debit')
      .forEach(t => {
        const monthKey = new Date(t.date).toISOString().slice(0, 7);
        monthlySpending[monthKey] = (monthlySpending[monthKey] || 0) + t.amount;
      });
    
    return Object.values(monthlySpending);
  }

  private static convertToMonthly(amount: number, timeframe: string): number {
    switch (timeframe) {
      case 'daily': return amount * 30;
      case 'weekly': return amount * 4.33;
      case 'monthly': return amount;
      case 'yearly': return amount / 12;
      default: return amount;
    }
  }

  static createAutomatedSavingsRule(
    recommendation: SavingsRecommendation,
    bankAccountId: string
  ): {
    ruleId: string;
    description: string;
    amount: number;
    frequency: string;
    startDate: number;
  } {
    return {
      ruleId: `auto-save-${recommendation.id}`,
      description: recommendation.title,
      amount: recommendation.potentialSavings,
      frequency: recommendation.timeframe,
      startDate: Date.now()
    };
  }

  static calculateSavingsProgress(
    savingsGoals: SavingsGoal[],
    currentSavings: number
  ): Array<{
    goalId: string;
    name: string;
    progress: number;
    onTrack: boolean;
    monthsRemaining: number;
    monthlyRequired: number;
  }> {
    return savingsGoals.map(goal => {
      const progress = (goal.currentAmount / goal.targetAmount) * 100;
      const monthsRemaining = Math.max(0, (goal.targetDate - Date.now()) / (30 * 24 * 60 * 60 * 1000));
      const monthlyRequired = monthsRemaining > 0 ? (goal.targetAmount - goal.currentAmount) / monthsRemaining : 0;

      // Determine if on track based on time remaining vs amount remaining
      const timeProgress = monthsRemaining > 0 ?
        ((goal.targetDate - goal.createdAt) - (goal.targetDate - Date.now())) / (goal.targetDate - goal.createdAt) * 100 : 100;
      const onTrack = progress >= timeProgress * 0.9; // Allow 10% tolerance

      return {
        goalId: goal.id,
        name: goal.name,
        progress: Math.min(progress, 100),
        onTrack,
        monthsRemaining: Math.round(monthsRemaining),
        monthlyRequired: Math.max(0, monthlyRequired)
      };
    });
  }
}
