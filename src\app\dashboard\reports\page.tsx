"use client";

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { SpendingChart } from '@/components/charts/spending-chart';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTransactions, useBudgets, useBankAccounts } from '@/hooks/useRealtime';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  Download, 
  Calendar, 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  PieChart,
  FileText,
  Filter,
  Share,
  Mail,
  Printer
} from 'lucide-react';

const timeRanges = [
  { id: 'week', label: 'This Week' },
  { id: 'month', label: 'This Month' },
  { id: 'quarter', label: 'This Quarter' },
  { id: 'year', label: 'This Year' },
  { id: 'custom', label: 'Custom Range' },
];

const reportTypes = [
  { id: 'spending', label: 'Spending Analysis', icon: PieChart },
  { id: 'income', label: 'Income Report', icon: TrendingUp },
  { id: 'budget', label: 'Budget Performance', icon: BarChart3 },
  { id: 'trends', label: 'Financial Trends', icon: TrendingUp },
  { id: 'tax', label: 'Tax Summary', icon: FileText },
];

export default function ReportsPage() {
  const { transactionsWithBalance } = useTransactions();
  const { budgetProgress } = useBudgets();
  const { accounts, totalBalance } = useBankAccounts();
  
  const [selectedTimeRange, setSelectedTimeRange] = useState('month');
  const [selectedReportType, setSelectedReportType] = useState('spending');
  const [showCustomRange, setShowCustomRange] = useState(false);

  // Calculate date range
  const dateRange = useMemo(() => {
    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (selectedTimeRange) {
      case 'week':
        const startOfWeek = new Date(startOfDay);
        startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
        return { start: startOfWeek, end: now };
      
      case 'month':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        return { start: startOfMonth, end: now };
      
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3);
        const startOfQuarter = new Date(now.getFullYear(), quarter * 3, 1);
        return { start: startOfQuarter, end: now };
      
      case 'year':
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        return { start: startOfYear, end: now };
      
      default:
        return { start: startOfDay, end: now };
    }
  }, [selectedTimeRange]);

  // Filter transactions by date range
  const filteredTransactions = useMemo(() => {
    if (!transactionsWithBalance?.transactions) return [];
    
    return transactionsWithBalance.transactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate >= dateRange.start && transactionDate <= dateRange.end;
    });
  }, [transactionsWithBalance, dateRange]);

  // Calculate spending by category
  const spendingByCategory = useMemo(() => {
    const categoryTotals: Record<string, number> = {};
    
    filteredTransactions
      .filter(t => t.type === 'debit')
      .forEach(transaction => {
        const category = transaction.category;
        categoryTotals[category] = (categoryTotals[category] || 0) + Math.abs(transaction.amount);
      });

    return Object.entries(categoryTotals)
      .map(([category, amount]) => ({ category, amount }))
      .sort((a, b) => b.amount - a.amount);
  }, [filteredTransactions]);

  // Calculate income vs expenses
  const incomeVsExpenses = useMemo(() => {
    const income = filteredTransactions
      .filter(t => t.type === 'credit')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const expenses = filteredTransactions
      .filter(t => t.type === 'debit')
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);

    return { income, expenses, netIncome: income - expenses };
  }, [filteredTransactions]);

  // Calculate trends
  const trends = useMemo(() => {
    // This would typically compare with previous period
    const previousPeriodExpenses = 8500; // Mock data
    const currentExpenses = incomeVsExpenses.expenses;
    const change = ((currentExpenses - previousPeriodExpenses) / previousPeriodExpenses) * 100;
    
    return {
      expenseChange: change,
      isIncreasing: change > 0,
    };
  }, [incomeVsExpenses]);

  const handleExport = (format: 'pdf' | 'csv' | 'excel') => {
    // In a real app, this would generate and download the report
    console.log(`Exporting ${selectedReportType} report as ${format}`);
  };

  const renderSpendingReport = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(incomeVsExpenses.expenses)}
                </p>
              </div>
              <div className={`flex items-center space-x-1 ${
                trends.isIncreasing ? 'text-red-600' : 'text-green-600'
              }`}>
                {trends.isIncreasing ? (
                  <TrendingUp className="w-4 h-4" />
                ) : (
                  <TrendingDown className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">
                  {Math.abs(trends.expenseChange).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Income</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(incomeVsExpenses.income)}
                </p>
              </div>
              <Badge variant="outline" className="text-green-600 border-green-600">
                +5.2%
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Net Income</p>
                <p className={`text-2xl font-bold ${
                  incomeVsExpenses.netIncome >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formatCurrency(incomeVsExpenses.netIncome)}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Savings Rate</p>
                <p className="text-lg font-semibold">
                  {((incomeVsExpenses.netIncome / incomeVsExpenses.income) * 100).toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SpendingChart 
          data={spendingByCategory} 
          type="pie"
          title="Spending by Category"
        />
        <SpendingChart 
          data={spendingByCategory.slice(0, 5)} 
          type="bar"
          title="Top 5 Spending Categories"
        />
      </div>

      {/* Detailed Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Category Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {spendingByCategory.map((item, index) => {
              const percentage = (item.amount / incomeVsExpenses.expenses) * 100;
              return (
                <div key={item.category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 capitalize">
                        {item.category.replace('_', ' ')}
                      </div>
                      <div className="text-sm text-gray-600">
                        {percentage.toFixed(1)}% of total expenses
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">
                      {formatCurrency(item.amount)}
                    </div>
                    <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${Math.min(percentage, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderBudgetReport = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Budget Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {budgetProgress.map((budget) => (
              <div key={budget._id} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h3 className="font-medium text-gray-900">{budget.name}</h3>
                    <p className="text-sm text-gray-600 capitalize">
                      {budget.category.replace('_', ' ')} • {budget.period}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">
                      {formatCurrency(budget.actualSpent)} / {formatCurrency(budget.amount)}
                    </div>
                    <Badge 
                      variant={budget.isOverBudget ? "destructive" : budget.isNearLimit ? "secondary" : "outline"}
                      className={
                        budget.isOverBudget 
                          ? "text-red-600 border-red-600" 
                          : budget.isNearLimit 
                            ? "text-orange-600 border-orange-600"
                            : "text-green-600 border-green-600"
                      }
                    >
                      {budget.percentage.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      budget.isOverBudget 
                        ? 'bg-red-500' 
                        : budget.isNearLimit 
                          ? 'bg-orange-500' 
                          : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(budget.percentage, 100)}%` }}
                  ></div>
                </div>
                {budget.remaining > 0 && (
                  <p className="text-sm text-gray-600 mt-2">
                    {formatCurrency(budget.remaining)} remaining
                  </p>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Financial Reports</h1>
            <p className="text-gray-600">Analyze your financial data with detailed reports</p>
          </div>
          
          <div className="flex space-x-3">
            <Button variant="outline">
              <Share className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button variant="outline">
              <Mail className="w-4 h-4 mr-2" />
              Email
            </Button>
            <Button>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Report Type */}
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Report Type
                </label>
                <select
                  value={selectedReportType}
                  onChange={(e) => setSelectedReportType(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {reportTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Time Range */}
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time Range
                </label>
                <select
                  value={selectedTimeRange}
                  onChange={(e) => setSelectedTimeRange(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {timeRanges.map((range) => (
                    <option key={range.id} value={range.id}>
                      {range.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Export Options */}
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handleExport('pdf')}
                  className="flex items-center space-x-2"
                >
                  <FileText className="w-4 h-4" />
                  <span>PDF</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExport('csv')}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>CSV</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Content */}
        <div>
          {selectedReportType === 'spending' && renderSpendingReport()}
          {selectedReportType === 'budget' && renderBudgetReport()}
          {selectedReportType === 'income' && renderSpendingReport()}
          {selectedReportType === 'trends' && renderSpendingReport()}
          {selectedReportType === 'tax' && (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Tax Report Coming Soon</h3>
                <p className="text-gray-600">
                  We're working on comprehensive tax reporting features for South African tax requirements.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
