"use client";

import { useState, useRef, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useRealtimeData, useTransactions, useBudgets } from '@/hooks/useRealtime';
import { AIFinancialCoach, FinancialProfile } from '@/lib/ai-financial-coach';
import { AIPredictiveAnalytics } from '@/lib/ai-predictive-analytics';
import { 
  Send, 
  Brain, 
  User, 
  Lightbulb, 
  TrendingUp, 
  PiggyBank,
  AlertTriangle,
  Target,
  Mic,
  MicOff,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RefreshCw
} from 'lucide-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  suggestions?: string[];
  metadata?: any;
}

const quickPrompts = [
  {
    icon: TrendingUp,
    title: "Spending Analysis",
    prompt: "Analyze my spending patterns for this month and give me insights"
  },
  {
    icon: PiggyBank,
    title: "Savings Tips",
    prompt: "How can I save more money based on my current spending?"
  },
  {
    icon: Target,
    title: "Budget Optimization",
    prompt: "Help me optimize my budget for better financial health"
  },
  {
    icon: AlertTriangle,
    title: "Financial Health",
    prompt: "What's my current financial health score and how can I improve it?"
  }
];

export default function AIChatPage() {
  const { realtimeData } = useRealtimeData();
  const { transactionsWithBalance } = useTransactions();
  const { budgetProgress } = useBudgets();

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: "Hi! I'm your AI financial assistant powered by advanced analytics. I can provide personalized insights, predict future spending, detect anomalies, and coach you towards better financial health. What would you like to explore?",
      timestamp: Date.now(),
      suggestions: [
        "Analyze my financial health",
        "Predict my future spending",
        "Find spending anomalies",
        "Get personalized coaching"
      ]
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response (in a real app, this would call your AI service)
    setTimeout(() => {
      const aiResponse = generateAIResponse(content, realtimeData);
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1500);
  };

  const generateAIResponse = (userInput: string, data: any): ChatMessage => {
    const input = userInput.toLowerCase();

    let content = "";
    let suggestions: string[] = [];

    // Mock financial profile for demonstration
    const mockProfile: FinancialProfile = {
      monthlyIncome: 25000,
      monthlyExpenses: 18000,
      currentSavings: 45000,
      currentDebt: 15000,
      riskTolerance: 'moderate',
      age: 32,
      dependents: 2,
      employmentStatus: 'employed',
      goals: []
    };

    const transactions = transactionsWithBalance?.transactions || [];

    if (input.includes('health') || input.includes('analyze')) {
      const analysis = AIFinancialCoach.analyzeFinancialHealth(mockProfile, transactions);

      content = `## Your Financial Health Score: ${analysis.score}/100 📊

**Overall Assessment:**
${analysis.score >= 80 ? '🟢 Excellent' : analysis.score >= 60 ? '🟡 Good' : '🔴 Needs Improvement'}

**Key Insights:**
${analysis.insights.slice(0, 3).map(insight =>
  `• **${insight.title}**: ${insight.description}`
).join('\n')}

**Top Recommendations:**
${analysis.recommendations.slice(0, 2).map(rec =>
  `• **${rec.title}**: ${rec.description}`
).join('\n')}

Your financial health is ${analysis.score >= 70 ? 'on track' : 'improving'}. Focus on the recommendations above for better results.`;

      suggestions = [
        "Show detailed recommendations",
        "Predict future spending",
        "South African tax advice",
        "Investment suggestions"
      ];
    } else if (input.includes('predict') || input.includes('forecast')) {
      const forecast = AIPredictiveAnalytics.generateFinancialForecast(
        transactions,
        '3_months',
        mockProfile.currentSavings
      );

      content = `## 3-Month Financial Forecast 🔮

**Spending Predictions:**
${forecast.spendingPredictions.slice(0, 4).map(pred =>
  `• **${pred.category}**: R${pred.predictedAmount.toFixed(2)} (${pred.trend} trend, ${(pred.confidence * 100).toFixed(0)}% confidence)`
).join('\n')}

**Savings Projection:**
Expected to save R${forecast.savingsProjection.projected.toFixed(2)} over 3 months
Confidence: ${(forecast.savingsProjection.confidence * 100).toFixed(0)}%

**Key Recommendations:**
${forecast.recommendations.slice(0, 2).map(rec => `• ${rec}`).join('\n')}

This forecast is based on your historical patterns and seasonal trends.`;

      suggestions = [
        "Detect spending anomalies",
        "Optimize my budget",
        "Emergency fund planning",
        "Investment opportunities"
      ];
    } else if (input.includes('anomal') || input.includes('unusual')) {
      const anomalies = AIPredictiveAnalytics.detectAnomalies(transactions);

      if (anomalies.length > 0) {
        content = `## Spending Anomalies Detected 🚨

I found ${anomalies.length} unusual transaction${anomalies.length > 1 ? 's' : ''}:

${anomalies.slice(0, 3).map(anomaly =>
  `• **${anomaly.anomalyType.toUpperCase()}**: ${anomaly.description}
  Severity: ${anomaly.severity} | Confidence: ${(anomaly.confidence * 100).toFixed(0)}%
  Action: ${anomaly.suggestedAction}`
).join('\n\n')}

${anomalies.length > 3 ? `\n...and ${anomalies.length - 3} more anomalies detected.` : ''}

These anomalies might indicate errors, fraud, or changes in spending behavior.`;
      } else {
        content = `## No Anomalies Detected ✅

Great news! I didn't find any unusual spending patterns in your recent transactions. Your spending behavior appears consistent and normal.

This suggests good financial discipline and predictable spending habits.`;
      }

      suggestions = [
        "Set up fraud alerts",
        "Review transaction categories",
        "Analyze spending trends",
        "Budget optimization tips"
      ];
    } else if (input.includes('coach') || input.includes('advice')) {
      const saAdvice = AIFinancialCoach.getSouthAfricanSpecificAdvice(mockProfile);

      content = `## Personalized Financial Coaching 🎯

**South African Specific Recommendations:**

${saAdvice.slice(0, 2).map(advice =>
  `### ${advice.title}
  ${advice.description}

  **Steps to take:**
  ${advice.steps.map(step => `• ${step}`).join('\n')}

  **Expected outcome:** ${advice.expectedOutcome}
  **Timeframe:** ${advice.timeframe}`
).join('\n\n')}

These recommendations are tailored for South African financial regulations and opportunities.`;

      suggestions = [
        "Tax-free savings account",
        "Stokvel opportunities",
        "Retirement planning",
        "Emergency fund strategy"
      ];
    } else if (input.includes('spending') || input.includes('expenses')) {
      content = `Based on your recent transactions, here's your spending breakdown:

**Top Categories This Month:**
• Groceries: R2,450 (32%)
• Transport: R1,800 (24%)
• Entertainment: R950 (12%)
• Utilities: R850 (11%)
• Dining: R650 (9%)

**Key Insights:**
• Your grocery spending is 15% higher than last month
• You've saved R200 on transport by using more public transport
• Consider setting a budget limit of R2,200 for groceries

Would you like me to help you create a budget for any of these categories?`;
      
      suggestions = [
        "Create a grocery budget",
        "How to reduce transport costs",
        "Set up spending alerts",
        "Compare with last month"
      ];
    } else if (input.includes('budget')) {
      content = `I can help you create a personalized budget! Based on your spending patterns:

**Recommended Monthly Budget:**
• Income: R15,000
• Savings (20%): R3,000
• Fixed Expenses (50%): R7,500
  - Rent/Mortgage: R4,500
  - Utilities: R850
  - Insurance: R650
  - Other: R1,500
• Variable Expenses (30%): R4,500
  - Groceries: R2,200
  - Transport: R1,500
  - Entertainment: R800

**Next Steps:**
1. Set up automatic savings transfer
2. Create spending alerts for each category
3. Review and adjust monthly

Would you like me to set up any of these budgets for you?`;
      
      suggestions = [
        "Set up automatic savings",
        "Create spending alerts",
        "Adjust budget categories",
        "Track budget progress"
      ];
    } else if (input.includes('save') || input.includes('savings')) {
      content = `Great question! Here are personalized savings strategies for you:

**Quick Wins (Save R500-800/month):**
• Switch to generic brands for groceries: R200/month
• Use public transport 2 more days/week: R300/month
• Cancel unused subscriptions: R150/month
• Cook at home 3 more times/week: R250/month

**Medium-term Strategies:**
• Set up a high-yield savings account
• Automate 20% of income to savings
• Use the 52-week savings challenge
• Consider a stokvel for group savings

**Your Savings Goal Progress:**
Emergency Fund: R12,000 / R45,000 (27%)
Vacation Fund: R3,500 / R15,000 (23%)

Which savings strategy interests you most?`;
      
      suggestions = [
        "Set up automatic savings",
        "Join a stokvel",
        "Create emergency fund",
        "Track savings goals"
      ];
    } else if (input.includes('health') || input.includes('score')) {
      content = `Your Financial Health Score: **72/100** 📊

**Strengths:**
✅ Good savings rate (18% of income)
✅ Low debt-to-income ratio (15%)
✅ Consistent income
✅ Emergency fund building

**Areas for Improvement:**
⚠️ Budget variance too high (25% over in groceries)
⚠️ No investment portfolio
⚠️ Limited insurance coverage

**Action Plan to Reach 85/100:**
1. Stick to grocery budget (save R300/month)
2. Start investing R500/month in ETFs
3. Review insurance needs
4. Build emergency fund to 6 months expenses

Want me to help you with any of these improvements?`;
      
      suggestions = [
        "Start investing guide",
        "Review insurance options",
        "Improve budget discipline",
        "Build emergency fund"
      ];
    } else {
      content = `I understand you're asking about "${userInput}". I'm here to help with all your financial questions!

I can assist you with:
• **Spending Analysis** - Track where your money goes
• **Budget Creation** - Set up personalized budgets
• **Savings Strategies** - Find ways to save more
• **Investment Advice** - Start your investment journey
• **Bill Management** - Never miss a payment
• **Financial Goals** - Plan for your future
• **Stokvel Management** - Group savings made easy

What specific area would you like to explore?`;
      
      suggestions = [
        "Analyze my spending",
        "Create a budget",
        "Investment advice",
        "Savings strategies"
      ];
    }

    return {
      id: Date.now().toString(),
      type: 'ai',
      content,
      timestamp: Date.now(),
      suggestions
    };
  };

  const handleQuickPrompt = (prompt: string) => {
    handleSendMessage(prompt);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const toggleVoiceInput = () => {
    setIsListening(!isListening);
    // In a real app, you'd implement speech recognition here
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  return (
    <DashboardLayout>
      <div className="h-[calc(100vh-200px)] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Financial Assistant</h1>
            <p className="text-gray-600">Get personalized financial advice and insights</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-green-600 border-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              Online
            </Badge>
            <Button variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              New Chat
            </Button>
          </div>
        </div>

        {/* Quick Prompts */}
        {messages.length <= 1 && (
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              {quickPrompts.map((prompt, index) => {
                const Icon = prompt.icon;
                return (
                  <Card 
                    key={index}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleQuickPrompt(prompt.prompt)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Icon className="w-4 h-4 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium text-sm">{prompt.title}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}

        {/* Chat Messages */}
        <Card className="flex-1 flex flex-col">
          <CardContent className="flex-1 p-4 overflow-y-auto">
            <div className="space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex space-x-3 max-w-3xl ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    {/* Avatar */}
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.type === 'user' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gradient-to-r from-purple-500 to-blue-500 text-white'
                    }`}>
                      {message.type === 'user' ? (
                        <User className="w-4 h-4" />
                      ) : (
                        <Brain className="w-4 h-4" />
                      )}
                    </div>

                    {/* Message Content */}
                    <div className={`flex-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                      <div className={`inline-block p-3 rounded-lg ${
                        message.type === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                      
                      {/* Message Actions */}
                      <div className={`flex items-center space-x-2 mt-2 text-xs text-gray-500 ${
                        message.type === 'user' ? 'justify-end' : 'justify-start'
                      }`}>
                        <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                        {message.type === 'ai' && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyMessage(message.content)}
                              className="h-6 px-2"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-6 px-2">
                              <ThumbsUp className="w-3 h-3" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-6 px-2">
                              <ThumbsDown className="w-3 h-3" />
                            </Button>
                          </>
                        )}
                      </div>

                      {/* Suggestions */}
                      {message.suggestions && message.suggestions.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-2">
                          {message.suggestions.map((suggestion, index) => (
                            <Button
                              key={index}
                              variant="outline"
                              size="sm"
                              onClick={() => handleSuggestionClick(suggestion)}
                              className="text-xs"
                            >
                              {suggestion}
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Loading indicator */}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="flex space-x-3 max-w-3xl">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 text-white flex items-center justify-center">
                      <Brain className="w-4 h-4" />
                    </div>
                    <div className="bg-gray-100 p-3 rounded-lg">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </CardContent>

          {/* Input Area */}
          <div className="border-t p-4">
            <div className="flex space-x-3">
              <div className="flex-1 relative">
                <Input
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputValue)}
                  placeholder="Ask me anything about your finances..."
                  disabled={isLoading}
                  className="pr-12"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleVoiceInput}
                  className={`absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 ${
                    isListening ? 'text-red-500' : 'text-gray-400'
                  }`}
                >
                  {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                </Button>
              </div>
              <Button 
                onClick={() => handleSendMessage(inputValue)}
                disabled={!inputValue.trim() || isLoading}
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="mt-2 text-xs text-gray-500 text-center">
              AI responses are generated based on your financial data. Always verify important decisions with a financial advisor.
            </div>
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
}
