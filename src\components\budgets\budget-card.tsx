"use client";

import { formatCurrency } from "@/lib/utils";
import { Budget } from "@/lib/types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AlertTriangle, TrendingUp, TrendingDown } from "lucide-react";

interface BudgetCardProps {
  budget: Budget & {
    actualSpent: number;
    percentage: number;
    isOverBudget: boolean;
    isNearLimit: boolean;
    remaining: number;
  };
}

export function BudgetCard({ budget }: BudgetCardProps) {
  const getStatusColor = () => {
    if (budget.isOverBudget) return "text-red-600";
    if (budget.isNearLimit) return "text-orange-600";
    return "text-green-600";
  };

  const getProgressColor = () => {
    if (budget.isOverBudget) return "bg-red-500";
    if (budget.isNearLimit) return "bg-orange-500";
    return "bg-green-500";
  };

  const getBadgeVariant = () => {
    if (budget.isOverBudget) return "destructive";
    if (budget.isNearLimit) return "secondary";
    return "default";
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{budget.name}</CardTitle>
          <Badge variant={getBadgeVariant()} className="capitalize">
            {budget.category}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progress</span>
            <span className={`font-medium ${getStatusColor()}`}>
              {budget.percentage.toFixed(1)}%
            </span>
          </div>
          <Progress 
            value={Math.min(budget.percentage, 100)} 
            className="h-2"
            indicatorClassName={getProgressColor()}
          />
        </div>

        {/* Amount Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="text-gray-600">Spent</div>
            <div className="font-semibold text-gray-900">
              {formatCurrency(budget.actualSpent)}
            </div>
          </div>
          <div>
            <div className="text-gray-600">Budget</div>
            <div className="font-semibold text-gray-900">
              {formatCurrency(budget.amount)}
            </div>
          </div>
        </div>

        {/* Remaining/Overspent */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-2">
            {budget.isOverBudget ? (
              <>
                <AlertTriangle className="w-4 h-4 text-red-500" />
                <span className="text-sm font-medium text-red-700">Over Budget</span>
              </>
            ) : budget.isNearLimit ? (
              <>
                <AlertTriangle className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium text-orange-700">Near Limit</span>
              </>
            ) : (
              <>
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium text-green-700">On Track</span>
              </>
            )}
          </div>
          
          <div className="text-right">
            <div className={`text-sm font-semibold ${getStatusColor()}`}>
              {budget.isOverBudget ? (
                <>-{formatCurrency(budget.actualSpent - budget.amount)}</>
              ) : (
                <>{formatCurrency(budget.remaining)} left</>
              )}
            </div>
          </div>
        </div>

        {/* Period Info */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span className="capitalize">{budget.period} budget</span>
          <span>
            {new Date(budget.startDate).toLocaleDateString()} - {new Date(budget.endDate).toLocaleDateString()}
          </span>
        </div>

        {/* Alert Settings */}
        {budget.alerts.enabled && (
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <AlertTriangle className="w-3 h-3" />
            <span>Alert at {budget.alerts.threshold}%</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
