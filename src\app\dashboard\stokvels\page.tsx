"use client";

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  Users, 
  Plus, 
  Calendar, 
  TrendingUp,
  PiggyBank,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  UserPlus,
  Settings,
  BarChart3,
  Download,
  Share
} from 'lucide-react';

interface Stokvel {
  id: string;
  name: string;
  description: string;
  type: 'savings' | 'investment' | 'burial' | 'grocery' | 'rotating_credit';
  memberCount: number;
  monthlyContribution: number;
  totalPool: number;
  myContribution: number;
  nextPayout: Date;
  payoutAmount: number;
  status: 'active' | 'pending' | 'completed';
  joinedAt: Date;
  isAdmin: boolean;
}

const mockStokvels: Stokvel[] = [
  {
    id: '1',
    name: 'Family Savings Circle',
    description: 'Monthly savings for family emergencies and goals',
    type: 'savings',
    memberCount: 12,
    monthlyContribution: 1000,
    totalPool: 144000,
    myContribution: 12000,
    nextPayout: new Date('2024-03-15'),
    payoutAmount: 12000,
    status: 'active',
    joinedAt: new Date('2023-01-15'),
    isAdmin: true
  },
  {
    id: '2',
    name: 'Investment Club',
    description: 'Pooling funds for JSE investments and property',
    type: 'investment',
    memberCount: 8,
    monthlyContribution: 2500,
    totalPool: 240000,
    myContribution: 30000,
    nextPayout: new Date('2024-12-01'),
    payoutAmount: 35000,
    status: 'active',
    joinedAt: new Date('2023-06-01'),
    isAdmin: false
  },
  {
    id: '3',
    name: 'Grocery Stokvel',
    description: 'Bulk buying for better prices on groceries',
    type: 'grocery',
    memberCount: 15,
    monthlyContribution: 800,
    totalPool: 144000,
    myContribution: 9600,
    nextPayout: new Date('2024-02-28'),
    payoutAmount: 9600,
    status: 'active',
    joinedAt: new Date('2023-03-01'),
    isAdmin: false
  }
];

const stokvelTypes = [
  { id: 'savings', name: 'Savings Stokvel', icon: PiggyBank, description: 'Traditional rotating savings' },
  { id: 'investment', name: 'Investment Club', icon: TrendingUp, description: 'Pooled investments in stocks, property' },
  { id: 'burial', name: 'Burial Society', icon: Users, description: 'Funeral and burial assistance' },
  { id: 'grocery', name: 'Grocery Stokvel', icon: DollarSign, description: 'Bulk buying for better prices' },
  { id: 'rotating_credit', name: 'Rotating Credit', icon: Target, description: 'Members take turns receiving payouts' }
];

export default function StokvelsPage() {
  const [selectedTab, setSelectedTab] = useState<'my-stokvels' | 'discover' | 'create'>('my-stokvels');
  const [selectedType, setSelectedType] = useState<string>('all');

  const totalContributions = mockStokvels.reduce((sum, s) => sum + s.myContribution, 0);
  const totalExpectedReturns = mockStokvels.reduce((sum, s) => sum + s.payoutAmount, 0);
  const activeStokvels = mockStokvels.filter(s => s.status === 'active').length;

  const getTypeIcon = (type: string) => {
    const typeInfo = stokvelTypes.find(t => t.id === type);
    return typeInfo?.icon || Users;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderMyStokvelsList = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Contributions</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalContributions)}</p>
              </div>
              <PiggyBank className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Expected Returns</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(totalExpectedReturns)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Stokvels</p>
                <p className="text-2xl font-bold text-gray-900">{activeStokvels}</p>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stokvels List */}
      <div className="space-y-4">
        {mockStokvels.map((stokvel) => {
          const Icon = getTypeIcon(stokvel.type);
          return (
            <Card key={stokvel.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{stokvel.name}</h3>
                        <Badge className={getStatusColor(stokvel.status)}>
                          {stokvel.status}
                        </Badge>
                        {stokvel.isAdmin && (
                          <Badge variant="outline" className="text-purple-600 border-purple-600">
                            Admin
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600 mb-3">{stokvel.description}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Members:</span>
                          <span className="ml-1 font-medium">{stokvel.memberCount}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Monthly:</span>
                          <span className="ml-1 font-medium">{formatCurrency(stokvel.monthlyContribution)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Total Pool:</span>
                          <span className="ml-1 font-medium">{formatCurrency(stokvel.totalPool)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Next Payout:</span>
                          <span className="ml-1 font-medium">{formatDate(stokvel.nextPayout)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <BarChart3 className="w-4 h-4 mr-2" />
                      Details
                    </Button>
                    {stokvel.isAdmin && (
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4 mr-2" />
                        Manage
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderDiscoverStokvels = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Discover Stokvels</h2>
        <p className="text-gray-600">Find and join stokvels in your area or create your own</p>
      </div>

      {/* Stokvel Types */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stokvelTypes.map((type) => {
          const Icon = type.icon;
          return (
            <Card key={type.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Icon className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{type.name}</h3>
                <p className="text-gray-600 text-sm mb-4">{type.description}</p>
                <Button variant="outline" className="w-full">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Find Groups
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderCreateStokvel = () => (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Create New Stokvel</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Stokvel Name
            </label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter stokvel name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type
            </label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              {stokvelTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe the purpose and goals of your stokvel"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Monthly Contribution
              </label>
              <input
                type="number"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="1000"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Members
              </label>
              <input
                type="number"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="12"
              />
            </div>
          </div>

          <div className="flex space-x-4">
            <Button className="flex-1">
              <Plus className="w-4 h-4 mr-2" />
              Create Stokvel
            </Button>
            <Button variant="outline" className="flex-1">
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Stokvels</h1>
            <p className="text-gray-600">Manage your group savings and investment clubs</p>
          </div>
          
          <div className="flex space-x-3">
            <Button variant="outline">
              <Share className="w-4 h-4 mr-2" />
              Invite Friends
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Stokvel
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'my-stokvels', label: 'My Stokvels', count: mockStokvels.length },
              { id: 'discover', label: 'Discover' },
              { id: 'create', label: 'Create New' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                {tab.count && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {selectedTab === 'my-stokvels' && renderMyStokvelsList()}
        {selectedTab === 'discover' && renderDiscoverStokvels()}
        {selectedTab === 'create' && renderCreateStokvel()}
      </div>
    </DashboardLayout>
  );
}
