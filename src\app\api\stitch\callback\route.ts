import { NextRequest, NextResponse } from 'next/server';
import { stitchAPI, categorizeTransaction } from '@/lib/stitch';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../../../../convex/_generated/api';
import { auth } from '@clerk/nextjs/server';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.redirect(new URL('/sign-in', request.url));
    }

    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    if (error) {
      console.error('Stitch OAuth error:', error);
      return NextResponse.redirect(
        new URL(`/dashboard/bank-accounts?error=${encodeURIComponent(error)}`, request.url)
      );
    }

    if (!code) {
      return NextResponse.redirect(
        new URL('/dashboard/bank-accounts?error=missing_code', request.url)
      );
    }

    // Exchange code for token
    const tokenResponse = await stitchAPI.exchangeCodeForToken(code);
    const userToken = tokenResponse.access_token;

    // Get user from Convex
    const user = await convex.query(api.userPreferences.getUserByClerkId, {
      clerkId: userId,
    });

    if (!user) {
      return NextResponse.redirect(
        new URL('/dashboard/bank-accounts?error=user_not_found', request.url)
      );
    }

    // Fetch accounts from Stitch
    const stitchAccounts = await stitchAPI.getAccounts(userToken);

    // Store accounts in Convex
    const createdAccounts = [];
    for (const stitchAccount of stitchAccounts) {
      try {
        const accountId = await convex.mutation(api.bankAccounts.createBankAccount, {
          userId: user._id,
          accountId: stitchAccount.id,
          accountName: stitchAccount.name,
          accountType: stitchAccount.accountType,
          bankName: stitchAccount.bankName,
          balance: stitchAccount.balance,
          currency: stitchAccount.currency,
        });
        createdAccounts.push({ convexId: accountId, stitchId: stitchAccount.id });
      } catch (error) {
        console.error('Error creating account:', error);
        // Continue with other accounts even if one fails
      }
    }

    // Sync recent transactions for each account
    for (let i = 0; i < stitchAccounts.length; i++) {
      const stitchAccount = stitchAccounts[i];
      const createdAccount = createdAccounts[i];

      if (!createdAccount) continue;

      try {
        const transactions = await stitchAPI.getTransactions(userToken, stitchAccount.id, 50);

        const transactionData = transactions.map(transaction => ({
          userId: user._id,
          accountId: createdAccount.convexId,
          transactionId: transaction.id,
          amount: transaction.amount,
          currency: transaction.currency,
          description: transaction.description,
          category: categorizeTransaction(transaction.description, transaction.merchant) as any,
          date: new Date(transaction.date).getTime(),
          type: transaction.type,
          merchant: transaction.merchant,
        }));

        if (transactionData.length > 0) {
          await convex.mutation(api.transactions.bulkCreateTransactions, {
            transactions: transactionData,
          });
        }
      } catch (error) {
        console.error('Error syncing transactions for account:', stitchAccount.id, error);
      }
    }

    // Update sync status
    await convex.mutation(api.syncStatus.updateSyncStatus, {
      userId: user._id,
      provider: 'stitch',
      status: 'success',
    });

    // Create success notification
    await convex.mutation(api.notifications.createNotification, {
      userId: user._id,
      type: 'transaction_alert',
      title: 'Bank Accounts Connected',
      message: `Successfully connected ${stitchAccounts.length} bank account(s) via Stitch`,
      priority: 'medium',
      actionUrl: '/dashboard/bank-accounts',
      metadata: {
        accountCount: stitchAccounts.length,
        provider: 'stitch',
      },
    });

    // Log the activity
    await convex.mutation(api.syncStatus.logActivity, {
      userId: user._id,
      action: 'stitch_accounts_connected',
      entityType: 'bankAccount',
      details: {
        accountCount: stitchAccounts.length,
        accounts: stitchAccounts.map(acc => ({
          id: acc.id,
          name: acc.name,
          bankName: acc.bankName,
        })),
      },
    });

    // Redirect to success page
    return NextResponse.redirect(
      new URL(`/dashboard/bank-accounts?success=connected&count=${stitchAccounts.length}`, request.url)
    );

  } catch (error) {
    console.error('Stitch callback error:', error);

    // Try to update sync status to error if we have user context
    try {
      const { userId } = await auth();
      if (userId) {
        const user = await convex.query(api.userPreferences.getUserByClerkId, {
          clerkId: userId,
        });

        if (user) {
          await convex.mutation(api.syncStatus.updateSyncStatus, {
            userId: user._id,
            provider: 'stitch',
            status: 'error',
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    } catch (syncError) {
      console.error('Error updating sync status:', syncError);
    }

    return NextResponse.redirect(
      new URL(`/dashboard/bank-accounts?error=${encodeURIComponent('connection_failed')}`, request.url)
    );
  }
}
