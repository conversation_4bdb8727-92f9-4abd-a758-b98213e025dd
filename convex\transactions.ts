import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const createTransaction = mutation({
  args: {
    userId: v.id("users"),
    accountId: v.id("bankAccounts"),
    transactionId: v.string(),
    amount: v.number(),
    currency: v.string(),
    description: v.string(),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    subcategory: v.optional(v.string()),
    date: v.number(),
    type: v.union(v.literal("debit"), v.literal("credit")),
    merchant: v.optional(v.string()),
    location: v.optional(v.string()),
    isRecurring: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const transactionId = await ctx.db.insert("transactions", {
      ...args,
      isRecurring: args.isRecurring ?? false,
      tags: args.tags ?? [],
      createdAt: Date.now(),
    });

    // Log activity
    await ctx.db.insert("activityLog", {
      userId: args.userId,
      action: "transaction_created",
      entityType: "transaction",
      entityId: transactionId,
      details: {
        amount: args.amount,
        category: args.category,
        merchant: args.merchant,
      },
      createdAt: Date.now(),
    });

    // Check for alerts
    const absAmount = Math.abs(args.amount);
    const isLargeTransaction = absAmount > 1000; // R1000+ is considered large

    // Create transaction alert if needed
    if (isLargeTransaction || args.type === "debit") {
      await ctx.db.insert("notifications", {
        userId: args.userId,
        type: "transaction_alert",
        title: isLargeTransaction ? "Large Transaction Alert" : "New Transaction",
        message: `${args.type === "credit" ? "Received" : "Spent"} R${absAmount.toFixed(2)}${args.merchant ? ` at ${args.merchant}` : ""}`,
        priority: isLargeTransaction ? "high" : "low",
        isRead: false,
        actionUrl: "/dashboard/transactions",
        metadata: {
          transactionId: args.transactionId,
          amount: args.amount,
          merchant: args.merchant,
          isLargeTransaction,
        },
        createdAt: Date.now(),
      });
    }

    // Update account balance
    const account = await ctx.db.get(args.accountId);
    if (account) {
      const newBalance = args.type === "credit"
        ? account.balance + args.amount
        : account.balance - Math.abs(args.amount);

      await ctx.db.patch(args.accountId, {
        balance: newBalance,
        lastSynced: Date.now(),
        updatedAt: Date.now(),
      });
    }

    return transactionId;
  },
});

export const getTransactionsByUser = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    return await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);
  },
});

export const getTransactionsByAccount = query({
  args: { 
    accountId: v.id("bankAccounts"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    return await ctx.db
      .query("transactions")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .order("desc")
      .take(limit);
  },
});

export const getTransactionsByDateRange = query({
  args: {
    userId: v.id("users"),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => 
        q.and(
          q.gte(q.field("date"), args.startDate),
          q.lte(q.field("date"), args.endDate)
        )
      )
      .order("desc")
      .collect();
  },
});

export const getTransactionsByCategory = query({
  args: {
    userId: v.id("users"),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("category"), args.category));

    if (args.startDate && args.endDate) {
      query = query.filter((q) => 
        q.and(
          q.gte(q.field("date"), args.startDate!),
          q.lte(q.field("date"), args.endDate!)
        )
      );
    }

    return await query.order("desc").collect();
  },
});

export const updateTransactionCategory = mutation({
  args: {
    transactionId: v.id("transactions"),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    subcategory: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.transactionId, {
      category: args.category,
      subcategory: args.subcategory,
    });
  },
});

export const getSpendingByCategory = query({
  args: {
    userId: v.id("users"),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    const transactions = await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) =>
        q.and(
          q.gte(q.field("date"), args.startDate),
          q.lte(q.field("date"), args.endDate),
          q.eq(q.field("type"), "debit")
        )
      )
      .collect();

    const categoryTotals: Record<string, number> = {};

    transactions.forEach((transaction) => {
      const category = transaction.category;
      categoryTotals[category] = (categoryTotals[category] || 0) + Math.abs(transaction.amount);
    });

    return Object.entries(categoryTotals).map(([category, amount]) => ({
      category,
      amount,
    }));
  },
});

// Realtime transaction stream
export const getRealtimeTransactions = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;

    return await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);
  },
});

// Get transactions with realtime balance updates
export const getTransactionsWithBalance = query({
  args: {
    userId: v.id("users"),
    accountId: v.optional(v.id("bankAccounts")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;

    let query = ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId));

    if (args.accountId) {
      query = query.filter((q) => q.eq(q.field("accountId"), args.accountId));
    }

    const transactions = await query
      .order("desc")
      .take(limit);

    // Get current account balances
    const accountQuery = ctx.db
      .query("bankAccounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true));

    const accounts = args.accountId
      ? [await ctx.db.get(args.accountId)].filter(Boolean)
      : await accountQuery.collect();

    return {
      transactions,
      accounts,
      lastUpdated: Date.now(),
    };
  },
});

// Get pending transactions (for optimistic updates)
export const getPendingTransactions = query({
  args: { userId: v.id("users") },
  handler: async (_ctx, _args) => {
    // This would typically store transactions that are being processed
    // For now, we'll return an empty array as this would be handled by the client
    return [];
  },
});

// Bulk create transactions (for sync operations)
export const bulkCreateTransactions = mutation({
  args: {
    transactions: v.array(v.object({
      userId: v.id("users"),
      accountId: v.id("bankAccounts"),
      transactionId: v.string(),
      amount: v.number(),
      currency: v.string(),
      description: v.string(),
      category: v.union(
        v.literal("groceries"),
        v.literal("transport"),
        v.literal("entertainment"),
        v.literal("utilities"),
        v.literal("healthcare"),
        v.literal("education"),
        v.literal("shopping"),
        v.literal("dining"),
        v.literal("travel"),
        v.literal("insurance"),
        v.literal("investments"),
        v.literal("income"),
        v.literal("transfers"),
        v.literal("fees"),
        v.literal("other")
      ),
      subcategory: v.optional(v.string()),
      date: v.number(),
      type: v.union(v.literal("debit"), v.literal("credit")),
      merchant: v.optional(v.string()),
      location: v.optional(v.string()),
      isRecurring: v.optional(v.boolean()),
      tags: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, args) => {
    const createdIds = [];

    for (const transaction of args.transactions) {
      // Check if transaction already exists
      const existing = await ctx.db
        .query("transactions")
        .withIndex("by_user", (q) => q.eq("userId", transaction.userId))
        .filter((q) => q.eq(q.field("transactionId"), transaction.transactionId))
        .first();

      if (!existing) {
        const transactionId = await ctx.db.insert("transactions", {
          ...transaction,
          isRecurring: transaction.isRecurring ?? false,
          tags: transaction.tags ?? [],
          createdAt: Date.now(),
        });
        createdIds.push(transactionId);
      }
    }

    // Log bulk sync activity
    if (createdIds.length > 0) {
      await ctx.db.insert("activityLog", {
        userId: args.transactions[0].userId,
        action: "bulk_transactions_synced",
        entityType: "transaction",
        details: {
          count: createdIds.length,
          source: "bulk_sync",
        },
        createdAt: Date.now(),
      });
    }

    return {
      created: createdIds.length,
      skipped: args.transactions.length - createdIds.length,
      transactionIds: createdIds,
    };
  },
});
