/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as bankAccounts from "../bankAccounts.js";
import type * as budgets from "../budgets.js";
import type * as notifications from "../notifications.js";
import type * as syncStatus from "../syncStatus.js";
import type * as transactions from "../transactions.js";
import type * as userPreferences from "../userPreferences.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  bankAccounts: typeof bankAccounts;
  budgets: typeof budgets;
  notifications: typeof notifications;
  syncStatus: typeof syncStatus;
  transactions: typeof transactions;
  userPreferences: typeof userPreferences;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
