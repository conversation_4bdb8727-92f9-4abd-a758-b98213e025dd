"use client";

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AISecurityEngine } from '@/lib/ai-security';
import { Transaction } from '@/lib/types';
import { formatCurrency } from '@/lib/utils';
import { 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Lock,
  Eye,
  Smartphone,
  Wifi,
  CreditCard,
  Bell,
  Settings,
  Info,
  ExternalLink,
  RefreshCw,
  Zap,
  TrendingUp,
  Users,
  Globe,
  Lightbulb
} from 'lucide-react';

// Mock data
const mockTransactions: Transaction[] = [
  {
    id: '1',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx1',
    amount: 15000,
    currency: 'ZAR',
    description: 'Salary Deposit',
    category: 'income',
    date: Date.now() - *********,
    type: 'credit',
    isRecurring: true,
    tags: ['monthly', 'salary'],
    createdAt: Date.now(),
  },
  // Add some suspicious-looking transactions for demo
  {
    id: '2',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx2',
    amount: 50,
    currency: 'ZAR',
    description: 'ATM Withdrawal',
    category: 'other',
    date: Date.now() - 300000, // 5 minutes ago
    type: 'debit',
    isRecurring: false,
    tags: [],
    createdAt: Date.now(),
  },
  {
    id: '3',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx3',
    amount: 100,
    currency: 'ZAR',
    description: 'ATM Withdrawal',
    category: 'other',
    date: Date.now() - 180000, // 3 minutes ago
    type: 'debit',
    isRecurring: false,
    tags: [],
    createdAt: Date.now(),
  },
  // Add more normal transactions
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `mock-${i}`,
    userId: 'user1',
    accountId: 'acc1',
    transactionId: `tx-mock-${i}`,
    amount: Math.random() * 1000 + 50,
    currency: 'ZAR',
    description: `Transaction ${i}`,
    category: ['groceries', 'transport', 'entertainment', 'utilities', 'dining'][Math.floor(Math.random() * 5)] as any,
    date: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
    type: 'debit' as const,
    isRecurring: Math.random() > 0.8,
    tags: [],
    createdAt: Date.now(),
  }))
];

const mockUserProfile = {
  hasStrongPassword: true,
  hasTwoFactorAuth: false,
  lastPasswordChange: Date.now() - 45 * 24 * 60 * 60 * 1000, // 45 days ago
  deviceCount: 3,
  recentLoginLocations: ['Johannesburg', 'Pretoria'],
};

export default function SecurityPage() {
  const [selectedAlert, setSelectedAlert] = useState<string | null>(null);
  const [showFraudPatterns, setShowFraudPatterns] = useState(false);

  const securityReport = useMemo(() => {
    return AISecurityEngine.generateSecurityReport(mockTransactions, mockUserProfile);
  }, []);

  const fraudTrends = useMemo(() => {
    return AISecurityEngine.getSouthAfricanFraudTrends();
  }, []);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Security Center</h1>
            <p className="text-gray-600">Monitor your account security and protect against fraud</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Scan Now
            </Button>
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Security Settings
            </Button>
          </div>
        </div>

        {/* Security Score Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-blue-600" />
              <span>Security Score</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-6 gap-6">
              {/* Overall Score */}
              <div className="md:col-span-2">
                <div className="text-center">
                  <div className={`text-6xl font-bold ${getScoreColor(securityReport.securityScore.overall)}`}>
                    {securityReport.securityScore.overall}
                  </div>
                  <div className="text-lg text-gray-600 mt-2">Overall Security Score</div>
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-3 ${
                    securityReport.securityScore.overall >= 80 ? 'bg-green-100 text-green-800' :
                    securityReport.securityScore.overall >= 60 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {securityReport.securityScore.overall >= 80 ? 'Excellent' :
                     securityReport.securityScore.overall >= 60 ? 'Good' : 'Needs Improvement'}
                  </div>
                </div>
              </div>

              {/* Breakdown */}
              <div className="md:col-span-4 space-y-4">
                {Object.entries(securityReport.securityScore.breakdown).map(([key, score]) => (
                  <div key={key} className="flex items-center space-x-4">
                    <div className="w-32 text-sm font-medium text-gray-700 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </div>
                    <div className="flex-1">
                      <Progress value={score} className="h-2" />
                    </div>
                    <div className={`text-sm font-medium ${getScoreColor(score)}`}>
                      {score}/100
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                <span>Security Alerts</span>
                {securityReport.alerts.length > 0 && (
                  <Badge variant="destructive">{securityReport.alerts.length}</Badge>
                )}
              </div>
              <Button variant="outline" size="sm">
                <Bell className="w-4 h-4 mr-2" />
                Alert Settings
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {securityReport.alerts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Alerts</h3>
                <p className="text-gray-600">Your account security looks good. Keep up the great work!</p>
              </div>
            ) : (
              <div className="space-y-4">
                {securityReport.alerts.map((alert) => (
                  <div key={alert.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className={`w-5 h-5 ${
                          alert.severity === 'critical' ? 'text-red-600' :
                          alert.severity === 'high' ? 'text-orange-600' :
                          alert.severity === 'medium' ? 'text-yellow-600' :
                          'text-blue-600'
                        }`} />
                        <div>
                          <h3 className="font-semibold text-gray-900">{alert.title}</h3>
                          <p className="text-sm text-gray-600">{alert.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getSeverityColor(alert.severity)}>
                          {alert.severity}
                        </Badge>
                        {alert.actionRequired && (
                          <Badge variant="outline" className="text-red-600">
                            Action Required
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {alert.recommendations.length > 0 && (
                      <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">Recommended Actions:</h4>
                        <ul className="space-y-1">
                          {alert.recommendations.map((rec, index) => (
                            <li key={index} className="text-sm text-blue-800 flex items-start space-x-2">
                              <CheckCircle className="w-3 h-3 mt-0.5 text-blue-600" />
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    <div className="flex justify-end space-x-2 mt-4">
                      <Button variant="outline" size="sm">
                        Dismiss
                      </Button>
                      <Button size="sm">
                        Take Action
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Security Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Two-Factor Authentication */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Smartphone className="w-6 h-6 text-blue-600" />
                </div>
                <Badge variant={mockUserProfile.hasTwoFactorAuth ? "default" : "destructive"}>
                  {mockUserProfile.hasTwoFactorAuth ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Two-Factor Authentication</h3>
              <p className="text-sm text-gray-600 mb-4">
                Add an extra layer of security to your account
              </p>
              <Button variant="outline" size="sm" className="w-full">
                {mockUserProfile.hasTwoFactorAuth ? 'Manage' : 'Enable'} 2FA
              </Button>
            </CardContent>
          </Card>

          {/* Password Security */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Lock className="w-6 h-6 text-green-600" />
                </div>
                <Badge variant={mockUserProfile.hasStrongPassword ? "default" : "destructive"}>
                  {mockUserProfile.hasStrongPassword ? 'Strong' : 'Weak'}
                </Badge>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Password Security</h3>
              <p className="text-sm text-gray-600 mb-4">
                Last changed 45 days ago
              </p>
              <Button variant="outline" size="sm" className="w-full">
                Change Password
              </Button>
            </CardContent>
          </Card>

          {/* Device Management */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Smartphone className="w-6 h-6 text-purple-600" />
                </div>
                <Badge variant="outline">
                  {mockUserProfile.deviceCount} Devices
                </Badge>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Connected Devices</h3>
              <p className="text-sm text-gray-600 mb-4">
                Manage devices with access to your account
              </p>
              <Button variant="outline" size="sm" className="w-full">
                Manage Devices
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* South African Fraud Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="w-5 h-5 text-red-600" />
              <span>South African Fraud Trends</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              {fraudTrends.map((trend, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">{trend.trend}</h3>
                  <p className="text-sm text-gray-600 mb-3">{trend.description}</p>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Prevention Tips:</h4>
                    <ul className="space-y-1">
                      {trend.prevention.slice(0, 3).map((tip, tipIndex) => (
                        <li key={tipIndex} className="text-sm text-gray-600 flex items-start space-x-2">
                          <CheckCircle className="w-3 h-3 mt-0.5 text-green-600" />
                          <span>{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Security Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lightbulb className="w-5 h-5 text-yellow-600" />
              <span>Security Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              {securityReport.recommendations.slice(0, 8).map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                  <span className="text-sm text-gray-700">{recommendation}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
