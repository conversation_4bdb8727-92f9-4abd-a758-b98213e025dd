import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

export const createNotification = mutation({
  args: {
    userId: v.id("users"),
    type: v.union(
      v.literal("budget_alert"),
      v.literal("transaction_alert"),
      v.literal("savings_milestone"),
      v.literal("bill_reminder"),
      v.literal("security_alert"),
      v.literal("ai_insight"),
      v.literal("stokvel_update")
    ),
    title: v.string(),
    message: v.string(),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("urgent")),
    actionUrl: v.optional(v.string()),
    metadata: v.optional(v.any()),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const notificationId = await ctx.db.insert("notifications", {
      ...args,
      isRead: false,
      createdAt: Date.now(),
    });

    return notificationId;
  },
});

export const getNotificationsByUser = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()),
    unreadOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    let query = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId));

    if (args.unreadOnly) {
      query = query.filter((q) => q.eq(q.field("isRead"), false));
    }

    // Filter out expired notifications
    const now = Date.now();
    query = query.filter((q) => 
      q.or(
        q.eq(q.field("expiresAt"), undefined),
        q.gt(q.field("expiresAt"), now)
      )
    );

    return await query
      .order("desc")
      .take(limit);
  },
});

export const markNotificationAsRead = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.notificationId, {
      isRead: true,
    });
  },
});

export const markAllNotificationsAsRead = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user_unread", (q) => 
        q.eq("userId", args.userId).eq("isRead", false)
      )
      .collect();

    for (const notification of unreadNotifications) {
      await ctx.db.patch(notification._id, {
        isRead: true,
      });
    }

    return { count: unreadNotifications.length };
  },
});

export const getUnreadCount = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const count = await ctx.db
      .query("notifications")
      .withIndex("by_user_unread", (q) => 
        q.eq("userId", args.userId).eq("isRead", false)
      )
      .collect();

    return count.length;
  },
});

export const deleteNotification = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.notificationId);
  },
});

export const deleteExpiredNotifications = mutation({
  args: {},
  handler: async (ctx, args) => {
    const now = Date.now();
    const expiredNotifications = await ctx.db
      .query("notifications")
      .filter((q) => 
        q.and(
          q.neq(q.field("expiresAt"), undefined),
          q.lt(q.field("expiresAt"), now)
        )
      )
      .collect();

    for (const notification of expiredNotifications) {
      await ctx.db.delete(notification._id);
    }

    return { deletedCount: expiredNotifications.length };
  },
});

// Helper function to create budget alerts
export const createBudgetAlert = mutation({
  args: {
    userId: v.id("users"),
    budgetName: v.string(),
    category: v.string(),
    percentage: v.number(),
    isOverBudget: v.boolean(),
  },
  handler: async (ctx, args) => {
    const title = args.isOverBudget 
      ? `Budget Exceeded: ${args.budgetName}`
      : `Budget Alert: ${args.budgetName}`;
    
    const message = args.isOverBudget
      ? `You've exceeded your ${args.category} budget by ${(args.percentage - 100).toFixed(1)}%`
      : `You've used ${args.percentage.toFixed(1)}% of your ${args.category} budget`;

    const priority = args.isOverBudget ? "high" : "medium";

    return await ctx.db.insert("notifications", {
      userId: args.userId,
      type: "budget_alert",
      title,
      message,
      priority,
      isRead: false,
      actionUrl: "/dashboard/budgets",
      metadata: {
        budgetName: args.budgetName,
        category: args.category,
        percentage: args.percentage,
        isOverBudget: args.isOverBudget,
      },
      createdAt: Date.now(),
    });
  },
});

// Helper function to create transaction alerts
export const createTransactionAlert = mutation({
  args: {
    userId: v.id("users"),
    transactionId: v.string(),
    amount: v.number(),
    merchant: v.optional(v.string()),
    isLargeTransaction: v.boolean(),
    isSuspicious: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const title = args.isSuspicious 
      ? "Suspicious Transaction Detected"
      : args.isLargeTransaction 
        ? "Large Transaction Alert"
        : "New Transaction";
    
    const message = args.isSuspicious
      ? `A potentially suspicious transaction of R${Math.abs(args.amount).toFixed(2)} was detected`
      : args.isLargeTransaction
        ? `Large transaction of R${Math.abs(args.amount).toFixed(2)} ${args.merchant ? `at ${args.merchant}` : ''}`
        : `New transaction of R${Math.abs(args.amount).toFixed(2)} ${args.merchant ? `at ${args.merchant}` : ''}`;

    const priority = args.isSuspicious ? "urgent" : args.isLargeTransaction ? "high" : "low";

    return await ctx.db.insert("notifications", {
      userId: args.userId,
      type: "transaction_alert",
      title,
      message,
      priority,
      isRead: false,
      actionUrl: "/dashboard/transactions",
      metadata: {
        transactionId: args.transactionId,
        amount: args.amount,
        merchant: args.merchant,
        isLargeTransaction: args.isLargeTransaction,
        isSuspicious: args.isSuspicious,
      },
      createdAt: Date.now(),
    });
  },
});
