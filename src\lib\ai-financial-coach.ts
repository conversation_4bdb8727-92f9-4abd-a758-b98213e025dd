import { Transaction } from './types';

export interface FinancialGoal {
  id: string;
  type: 'savings' | 'debt_reduction' | 'investment' | 'emergency_fund' | 'retirement';
  title: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: Date;
  priority: 'low' | 'medium' | 'high';
  category?: string;
}

export interface FinancialProfile {
  monthlyIncome: number;
  monthlyExpenses: number;
  currentSavings: number;
  currentDebt: number;
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  age: number;
  dependents: number;
  employmentStatus: 'employed' | 'self_employed' | 'unemployed' | 'retired';
  goals: FinancialGoal[];
}

export interface AIInsight {
  id: string;
  type: 'spending_alert' | 'savings_opportunity' | 'investment_suggestion' | 'budget_optimization' | 'goal_progress';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  actionable: boolean;
  actions?: string[];
  estimatedSavings?: number;
  confidence: number;
  category?: string;
}

export interface CoachingRecommendation {
  id: string;
  type: 'immediate' | 'short_term' | 'long_term';
  title: string;
  description: string;
  steps: string[];
  expectedOutcome: string;
  timeframe: string;
  difficulty: 'easy' | 'medium' | 'hard';
  priority: number;
}

export class AIFinancialCoach {
  private static readonly SOUTH_AFRICAN_CONTEXT = {
    averageIncome: 25000, // ZAR per month
    inflationRate: 0.055, // 5.5% annual
    interestRates: {
      savings: 0.045, // 4.5%
      fixedDeposit: 0.065, // 6.5%
      homeLoan: 0.095, // 9.5%
      personalLoan: 0.15, // 15%
    },
    taxBrackets: [
      { min: 0, max: 237100, rate: 0.18 },
      { min: 237101, max: 370500, rate: 0.26 },
      { min: 370501, max: 512800, rate: 0.31 },
      { min: 512801, max: 673000, rate: 0.36 },
      { min: 673001, max: 857900, rate: 0.39 },
      { min: 857901, max: 1817000, rate: 0.41 },
      { min: 1817001, max: Infinity, rate: 0.45 },
    ],
    emergencyFundMonths: 6,
    retirementSavingsRate: 0.15, // 15% of income
  };

  static analyzeFinancialHealth(profile: FinancialProfile, transactions: Transaction[]): {
    score: number;
    insights: AIInsight[];
    recommendations: CoachingRecommendation[];
  } {
    const insights: AIInsight[] = [];
    const recommendations: CoachingRecommendation[] = [];

    // Calculate financial health score
    const score = this.calculateHealthScore(profile, transactions);

    // Generate insights
    insights.push(...this.generateSpendingInsights(profile, transactions));
    insights.push(...this.generateSavingsInsights(profile));
    insights.push(...this.generateDebtInsights(profile));
    insights.push(...this.generateGoalInsights(profile));

    // Generate recommendations
    recommendations.push(...this.generateImmediateRecommendations(profile, insights));
    recommendations.push(...this.generateLongTermRecommendations(profile));

    return { score, insights, recommendations };
  }

  private static calculateHealthScore(profile: FinancialProfile, transactions: Transaction[]): number {
    let score = 0;
    const maxScore = 100;

    // Income stability (20 points)
    if (profile.employmentStatus === 'employed') score += 20;
    else if (profile.employmentStatus === 'self_employed') score += 15;
    else score += 5;

    // Savings rate (25 points)
    const savingsRate = (profile.monthlyIncome - profile.monthlyExpenses) / profile.monthlyIncome;
    if (savingsRate >= 0.2) score += 25;
    else if (savingsRate >= 0.15) score += 20;
    else if (savingsRate >= 0.1) score += 15;
    else if (savingsRate >= 0.05) score += 10;
    else if (savingsRate > 0) score += 5;

    // Emergency fund (20 points)
    const emergencyFundTarget = profile.monthlyExpenses * this.SOUTH_AFRICAN_CONTEXT.emergencyFundMonths;
    const emergencyFundRatio = profile.currentSavings / emergencyFundTarget;
    if (emergencyFundRatio >= 1) score += 20;
    else if (emergencyFundRatio >= 0.75) score += 15;
    else if (emergencyFundRatio >= 0.5) score += 10;
    else if (emergencyFundRatio >= 0.25) score += 5;

    // Debt-to-income ratio (20 points)
    const debtToIncomeRatio = profile.currentDebt / (profile.monthlyIncome * 12);
    if (debtToIncomeRatio <= 0.1) score += 20;
    else if (debtToIncomeRatio <= 0.2) score += 15;
    else if (debtToIncomeRatio <= 0.3) score += 10;
    else if (debtToIncomeRatio <= 0.4) score += 5;

    // Goal progress (15 points)
    const goalProgress = this.calculateGoalProgress(profile.goals);
    score += Math.round(goalProgress * 15);

    return Math.min(score, maxScore);
  }

  private static generateSpendingInsights(profile: FinancialProfile, transactions: Transaction[]): AIInsight[] {
    const insights: AIInsight[] = [];

    // Analyze spending patterns
    const monthlySpending = transactions
      .filter(t => t.type === 'debit' && this.isCurrentMonth(t.date))
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);

    if (monthlySpending > profile.monthlyExpenses * 1.1) {
      insights.push({
        id: 'overspending',
        type: 'spending_alert',
        title: 'Overspending Alert',
        description: `You've spent R${monthlySpending.toFixed(2)} this month, which is ${((monthlySpending / profile.monthlyExpenses - 1) * 100).toFixed(1)}% above your budget.`,
        impact: 'high',
        actionable: true,
        actions: ['Review recent transactions', 'Set spending alerts', 'Create stricter budget categories'],
        confidence: 0.9,
      });
    }

    // Category-specific insights
    const categorySpending = this.analyzeCategorySpending(transactions);
    Object.entries(categorySpending).forEach(([category, amount]) => {
      if (this.isHighSpendingCategory(category, amount, profile.monthlyIncome)) {
        insights.push({
          id: `high-${category}`,
          type: 'spending_alert',
          title: `High ${category} Spending`,
          description: `Your ${category} spending is above average for your income level.`,
          impact: 'medium',
          actionable: true,
          actions: [`Review ${category} expenses`, `Set ${category} budget limit`],
          confidence: 0.8,
          category,
        });
      }
    });

    return insights;
  }

  private static generateSavingsInsights(profile: FinancialProfile): AIInsight[] {
    const insights: AIInsight[] = [];
    const savingsRate = (profile.monthlyIncome - profile.monthlyExpenses) / profile.monthlyIncome;

    if (savingsRate < 0.1) {
      insights.push({
        id: 'low-savings',
        type: 'savings_opportunity',
        title: 'Low Savings Rate',
        description: `You're saving ${(savingsRate * 100).toFixed(1)}% of your income. Aim for at least 10-15%.`,
        impact: 'high',
        actionable: true,
        actions: ['Reduce discretionary spending', 'Automate savings', 'Find additional income sources'],
        estimatedSavings: profile.monthlyIncome * 0.05,
        confidence: 0.85,
      });
    }

    // Emergency fund analysis
    const emergencyFundTarget = profile.monthlyExpenses * this.SOUTH_AFRICAN_CONTEXT.emergencyFundMonths;
    if (profile.currentSavings < emergencyFundTarget) {
      insights.push({
        id: 'emergency-fund',
        type: 'savings_opportunity',
        title: 'Build Emergency Fund',
        description: `You need R${(emergencyFundTarget - profile.currentSavings).toFixed(2)} more for a 6-month emergency fund.`,
        impact: 'high',
        actionable: true,
        actions: ['Set up automatic emergency fund transfer', 'Use high-yield savings account'],
        confidence: 0.95,
      });
    }

    return insights;
  }

  private static generateDebtInsights(profile: FinancialProfile): AIInsight[] {
    const insights: AIInsight[] = [];

    if (profile.currentDebt > 0) {
      const debtToIncomeRatio = profile.currentDebt / (profile.monthlyIncome * 12);
      
      if (debtToIncomeRatio > 0.3) {
        insights.push({
          id: 'high-debt',
          type: 'spending_alert',
          title: 'High Debt Burden',
          description: `Your debt-to-income ratio is ${(debtToIncomeRatio * 100).toFixed(1)}%. Consider debt consolidation.`,
          impact: 'high',
          actionable: true,
          actions: ['Debt consolidation', 'Debt snowball method', 'Increase debt payments'],
          confidence: 0.9,
        });
      }
    }

    return insights;
  }

  private static generateGoalInsights(profile: FinancialProfile): AIInsight[] {
    const insights: AIInsight[] = [];

    profile.goals.forEach(goal => {
      const progress = goal.currentAmount / goal.targetAmount;
      const timeRemaining = goal.targetDate.getTime() - Date.now();
      const monthsRemaining = timeRemaining / (1000 * 60 * 60 * 24 * 30);

      if (monthsRemaining > 0) {
        const requiredMonthlySavings = (goal.targetAmount - goal.currentAmount) / monthsRemaining;
        const currentSavingsRate = (profile.monthlyIncome - profile.monthlyExpenses) / profile.monthlyIncome;
        const availableMonthlySavings = profile.monthlyIncome * currentSavingsRate;

        if (requiredMonthlySavings > availableMonthlySavings) {
          insights.push({
            id: `goal-${goal.id}`,
            type: 'goal_progress',
            title: `${goal.title} Goal at Risk`,
            description: `You need to save R${requiredMonthlySavings.toFixed(2)}/month but only have R${availableMonthlySavings.toFixed(2)} available.`,
            impact: 'medium',
            actionable: true,
            actions: ['Adjust goal timeline', 'Increase income', 'Reduce expenses'],
            confidence: 0.85,
          });
        }
      }
    });

    return insights;
  }

  private static generateImmediateRecommendations(profile: FinancialProfile, insights: AIInsight[]): CoachingRecommendation[] {
    const recommendations: CoachingRecommendation[] = [];

    // High-impact immediate actions
    if (insights.some(i => i.type === 'spending_alert' && i.impact === 'high')) {
      recommendations.push({
        id: 'spending-review',
        type: 'immediate',
        title: 'Review and Categorize All Expenses',
        description: 'Identify areas where you can cut back on spending immediately.',
        steps: [
          'Export last 3 months of transactions',
          'Categorize each expense',
          'Identify top 5 spending categories',
          'Set reduction targets for each category',
          'Implement spending alerts'
        ],
        expectedOutcome: 'Reduce monthly expenses by 10-15%',
        timeframe: '1 week',
        difficulty: 'easy',
        priority: 1,
      });
    }

    return recommendations;
  }

  private static generateLongTermRecommendations(profile: FinancialProfile): CoachingRecommendation[] {
    const recommendations: CoachingRecommendation[] = [];

    // Retirement planning
    const retirementSavings = profile.monthlyIncome * this.SOUTH_AFRICAN_CONTEXT.retirementSavingsRate;
    recommendations.push({
      id: 'retirement-planning',
      type: 'long_term',
      title: 'Start Retirement Planning',
      description: 'Begin contributing to retirement annuities and pension funds.',
      steps: [
        'Research retirement annuity providers',
        'Calculate required retirement savings',
        'Set up automatic contributions',
        'Review and adjust annually'
      ],
      expectedOutcome: `Save R${retirementSavings.toFixed(2)} monthly for retirement`,
      timeframe: '30+ years',
      difficulty: 'medium',
      priority: 2,
    });

    return recommendations;
  }

  private static isCurrentMonth(timestamp: number): boolean {
    const date = new Date(timestamp);
    const now = new Date();
    return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
  }

  private static analyzeCategorySpending(transactions: Transaction[]): Record<string, number> {
    const categorySpending: Record<string, number> = {};
    
    transactions
      .filter(t => t.type === 'debit' && this.isCurrentMonth(t.date))
      .forEach(t => {
        categorySpending[t.category] = (categorySpending[t.category] || 0) + Math.abs(t.amount);
      });

    return categorySpending;
  }

  private static isHighSpendingCategory(category: string, amount: number, monthlyIncome: number): boolean {
    const thresholds: Record<string, number> = {
      groceries: 0.15, // 15% of income
      transport: 0.10, // 10% of income
      entertainment: 0.05, // 5% of income
      dining: 0.08, // 8% of income
      shopping: 0.05, // 5% of income
    };

    const threshold = thresholds[category] || 0.03; // 3% default
    return amount > monthlyIncome * threshold;
  }

  private static calculateGoalProgress(goals: FinancialGoal[]): number {
    if (goals.length === 0) return 0;
    
    const totalProgress = goals.reduce((sum, goal) => {
      return sum + (goal.currentAmount / goal.targetAmount);
    }, 0);

    return totalProgress / goals.length;
  }

  // South African specific recommendations
  static getSouthAfricanSpecificAdvice(profile: FinancialProfile): CoachingRecommendation[] {
    const recommendations: CoachingRecommendation[] = [];

    // Tax-free savings account
    recommendations.push({
      id: 'tfsa',
      type: 'short_term',
      title: 'Maximize Tax-Free Savings Account',
      description: 'Use your annual R36,000 TFSA allowance for tax-free growth.',
      steps: [
        'Open TFSA with reputable provider',
        'Set up monthly R3,000 contribution',
        'Choose appropriate investment mix',
        'Monitor performance quarterly'
      ],
      expectedOutcome: 'Tax-free investment growth on R36,000 annually',
      timeframe: '1 year',
      difficulty: 'easy',
      priority: 1,
    });

    // Stokvel participation
    if (profile.monthlyIncome > 15000) {
      recommendations.push({
        id: 'stokvel',
        type: 'short_term',
        title: 'Join or Start a Stokvel',
        description: 'Participate in group savings for better financial discipline.',
        steps: [
          'Research local stokvels',
          'Understand terms and conditions',
          'Start with small monthly contribution',
          'Consider investment stokvels for growth'
        ],
        expectedOutcome: 'Disciplined savings and potential investment returns',
        timeframe: '6 months',
        difficulty: 'medium',
        priority: 2,
      });
    }

    return recommendations;
  }
}
