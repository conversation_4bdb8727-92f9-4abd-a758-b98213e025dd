"use client";

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AISavingsEngine } from '@/lib/ai-savings';
import { Transaction, SavingsGoal } from '@/lib/types';
import { formatCurrency } from '@/lib/utils';
import { 
  Plus, 
  Brain, 
  TrendingUp, 
  Target, 
  Lightbulb,
  Shield,
  Zap,
  Clock,
  DollarSign,
  PiggyBank,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

// Mock data for demonstration
const mockTransactions: Transaction[] = [
  {
    id: '1',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx1',
    amount: 15000,
    currency: 'ZAR',
    description: 'Salary Deposit',
    category: 'income',
    date: Date.now() - *********,
    type: 'credit',
    isRecurring: true,
    tags: ['monthly', 'salary'],
    createdAt: Date.now(),
  },
  // Add more mock transactions for better analysis
  ...Array.from({ length: 30 }, (_, i) => ({
    id: `mock-${i}`,
    userId: 'user1',
    accountId: 'acc1',
    transactionId: `tx-mock-${i}`,
    amount: Math.random() * 1000 + 100,
    currency: 'ZAR',
    description: `Transaction ${i}`,
    category: ['groceries', 'transport', 'entertainment', 'utilities', 'dining'][Math.floor(Math.random() * 5)] as any,
    date: Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000,
    type: 'debit' as const,
    isRecurring: Math.random() > 0.8,
    tags: [],
    createdAt: Date.now(),
  }))
];

const mockSavingsGoals: SavingsGoal[] = [
  {
    id: '1',
    userId: 'user1',
    name: 'Emergency Fund',
    description: 'Build 6 months of expenses',
    targetAmount: 50000,
    currentAmount: 15000,
    targetDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
    priority: 'high',
    category: 'emergency',
    isActive: true,
    createdAt: Date.now() - 90 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now(),
  },
  {
    id: '2',
    userId: 'user1',
    name: 'Vacation to Cape Town',
    description: 'Family vacation fund',
    targetAmount: 25000,
    currentAmount: 8000,
    targetDate: Date.now() + 180 * 24 * 60 * 60 * 1000,
    priority: 'medium',
    category: 'travel',
    isActive: true,
    createdAt: Date.now() - 60 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now(),
  },
];

export default function SavingsPage() {
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [selectedRecommendation, setSelectedRecommendation] = useState<string | null>(null);

  const monthlyIncome = 15000; // Mock monthly income
  const currentSavings = 23000; // Mock current savings

  const savingsAnalysis = useMemo(() => {
    return AISavingsEngine.analyzeSavings(mockTransactions, mockSavingsGoals, monthlyIncome, currentSavings);
  }, []);

  const savingsProgress = useMemo(() => {
    return AISavingsEngine.calculateSavingsProgress(mockSavingsGoals, currentSavings);
  }, []);

  const totalGoalAmount = mockSavingsGoals.reduce((sum, goal) => sum + goal.targetAmount, 0);
  const totalCurrentAmount = mockSavingsGoals.reduce((sum, goal) => sum + goal.currentAmount, 0);
  const overallProgress = (totalCurrentAmount / totalGoalAmount) * 100;

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Savings</h1>
            <p className="text-gray-600">AI-powered savings recommendations and goal tracking</p>
          </div>
          <div className="flex space-x-3">
            <Button 
              variant="outline"
              onClick={() => setShowAIAnalysis(!showAIAnalysis)}
            >
              <Brain className="w-4 h-4 mr-2" />
              AI Analysis
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Goal
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <PiggyBank className="w-6 h-6 text-green-600" />
                </div>
                <span className="text-sm text-gray-500">Current Savings</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(currentSavings)}</div>
              <div className="text-sm text-green-600">
                {(savingsAnalysis.currentSavingsRate * 100).toFixed(1)}% savings rate
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-blue-600" />
                </div>
                <span className="text-sm text-gray-500">Goals Progress</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">{overallProgress.toFixed(1)}%</div>
              <div className="text-sm text-blue-600">
                {formatCurrency(totalCurrentAmount)} of {formatCurrency(totalGoalAmount)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-purple-600" />
                </div>
                <span className="text-sm text-gray-500">Monthly Potential</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(savingsAnalysis.monthlySavingsPotential)}
              </div>
              <div className="text-sm text-purple-600">From AI recommendations</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-6 h-6 text-orange-600" />
                </div>
                <span className="text-sm text-gray-500">Emergency Fund</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {savingsAnalysis.emergencyFundStatus.monthsOfExpenses.toFixed(1)}
              </div>
              <div className="text-sm text-orange-600">Months covered</div>
            </CardContent>
          </Card>
        </div>

        {/* AI Analysis Panel */}
        {showAIAnalysis && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5 text-blue-600" />
                <span>AI Savings Analysis</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Emergency Fund Status */}
              <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                <h3 className="font-semibold text-orange-900 mb-2 flex items-center">
                  <Shield className="w-4 h-4 mr-2" />
                  Emergency Fund Status
                </h3>
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-orange-700">Current:</span>
                    <div className="font-semibold">{formatCurrency(savingsAnalysis.emergencyFundStatus.current)}</div>
                  </div>
                  <div>
                    <span className="text-orange-700">Recommended:</span>
                    <div className="font-semibold">{formatCurrency(savingsAnalysis.emergencyFundStatus.recommended)}</div>
                  </div>
                  <div>
                    <span className="text-orange-700">Coverage:</span>
                    <div className="font-semibold">
                      {savingsAnalysis.emergencyFundStatus.monthsOfExpenses.toFixed(1)} months
                    </div>
                  </div>
                </div>
                <Progress 
                  value={(savingsAnalysis.emergencyFundStatus.current / savingsAnalysis.emergencyFundStatus.recommended) * 100} 
                  className="mt-3"
                />
              </div>

              {/* Insights */}
              {savingsAnalysis.insights.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Lightbulb className="w-4 h-4 mr-2 text-yellow-500" />
                    Key Insights
                  </h3>
                  <div className="space-y-2">
                    {savingsAnalysis.insights.map((insight, index) => (
                      <div key={index} className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <p className="text-sm text-yellow-800">{insight}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Savings Goals */}
        <Card>
          <CardHeader>
            <CardTitle>Savings Goals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {savingsProgress.map((progress) => {
                const goal = mockSavingsGoals.find(g => g.id === progress.goalId);
                if (!goal) return null;
                
                return (
                  <div key={progress.goalId} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900">{progress.name}</h3>
                        <p className="text-sm text-gray-600">{goal.description}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-gray-900">
                          {formatCurrency(goal.currentAmount)} / {formatCurrency(goal.targetAmount)}
                        </div>
                        <div className={`text-sm ${progress.onTrack ? 'text-green-600' : 'text-red-600'}`}>
                          {progress.onTrack ? 'On track' : 'Behind schedule'}
                        </div>
                      </div>
                    </div>
                    
                    <Progress value={progress.progress} className="mb-3" />
                    
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>{progress.progress.toFixed(1)}% complete</span>
                      <span>
                        {progress.monthsRemaining} months remaining • 
                        {formatCurrency(progress.monthlyRequired)}/month needed
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* AI Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-blue-600" />
              <span>AI Savings Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {savingsAnalysis.recommendations.slice(0, 6).map((rec) => (
                <div key={rec.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-semibold text-gray-900">{rec.title}</h3>
                        <Badge className={getPriorityColor(rec.priority)}>
                          {rec.priority}
                        </Badge>
                        <Badge className={getRiskColor(rec.riskLevel)}>
                          {rec.riskLevel} risk
                        </Badge>
                        {rec.automatable && (
                          <Badge variant="outline">
                            <Zap className="w-3 h-3 mr-1" />
                            Auto
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{rec.description}</p>
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="flex items-center text-green-600">
                          <DollarSign className="w-4 h-4 mr-1" />
                          {formatCurrency(rec.potentialSavings)}/{rec.timeframe}
                        </span>
                        <span className="flex items-center text-blue-600">
                          <Target className="w-4 h-4 mr-1" />
                          {(rec.confidence * 100).toFixed(0)}% confidence
                        </span>
                      </div>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setSelectedRecommendation(
                        selectedRecommendation === rec.id ? null : rec.id
                      )}
                    >
                      {selectedRecommendation === rec.id ? 'Hide' : 'Details'}
                    </Button>
                  </div>
                  
                  {selectedRecommendation === rec.id && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-blue-900 mb-2">Action Steps:</h4>
                      <ul className="space-y-1">
                        {rec.actionSteps.map((step, index) => (
                          <li key={index} className="flex items-start text-sm text-blue-800">
                            <CheckCircle className="w-4 h-4 mr-2 mt-0.5 text-blue-600" />
                            {step}
                          </li>
                        ))}
                      </ul>
                      {rec.automatable && (
                        <div className="mt-3 pt-3 border-t border-blue-200">
                          <Button size="sm" className="w-full">
                            <Zap className="w-4 h-4 mr-2" />
                            Set Up Automatic Savings
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
