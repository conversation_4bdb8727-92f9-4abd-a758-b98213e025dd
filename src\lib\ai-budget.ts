import { Transaction, Budget, TransactionCategory } from './types';
import { TransactionCategorizer } from './ai-categorization';

export interface BudgetRecommendation {
  category: TransactionCategory;
  suggestedAmount: number;
  currentSpending: number;
  confidence: number;
  reasoning: string;
  costCuttingTips: string[];
  priority: 'high' | 'medium' | 'low';
}

export interface BudgetAnalysis {
  totalIncome: number;
  totalExpenses: number;
  savingsRate: number;
  recommendations: BudgetRecommendation[];
  insights: string[];
  riskFactors: string[];
}

export class AIBudgetAnalyzer {
  private static readonly SAVINGS_TARGET = 0.2; // 20% savings rate
  private static readonly ESSENTIAL_CATEGORIES: TransactionCategory[] = [
    'groceries', 'utilities', 'transport', 'healthcare', 'insurance'
  ];
  
  private static readonly DISCRETIONARY_CATEGORIES: TransactionCategory[] = [
    'entertainment', 'dining', 'shopping', 'travel'
  ];

  static analyzeBudget(transactions: Transaction[], timeframeMonths: number = 3): BudgetAnalysis {
    const analysis = TransactionCategorizer.analyzeSpendingPatterns(transactions);
    const monthlyIncome = this.calculateMonthlyIncome(transactions, timeframeMonths);
    const monthlyExpenses = this.calculateMonthlyExpenses(transactions, timeframeMonths);
    
    const savingsRate = monthlyIncome > 0 ? (monthlyIncome - monthlyExpenses) / monthlyIncome : 0;
    
    const recommendations = this.generateBudgetRecommendations(
      analysis.categoryBreakdown,
      monthlyIncome,
      timeframeMonths
    );
    
    const insights = this.generateInsights(transactions, monthlyIncome, monthlyExpenses, savingsRate);
    const riskFactors = this.identifyRiskFactors(transactions, analysis);
    
    return {
      totalIncome: monthlyIncome,
      totalExpenses: monthlyExpenses,
      savingsRate,
      recommendations,
      insights,
      riskFactors
    };
  }

  private static calculateMonthlyIncome(transactions: Transaction[], months: number): number {
    const incomeTransactions = transactions.filter(t => t.type === 'credit' && t.category === 'income');
    const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);
    return totalIncome / months;
  }

  private static calculateMonthlyExpenses(transactions: Transaction[], months: number): number {
    const expenseTransactions = transactions.filter(t => t.type === 'debit');
    const totalExpenses = expenseTransactions.reduce((sum, t) => sum + t.amount, 0);
    return totalExpenses / months;
  }

  private static generateBudgetRecommendations(
    categoryBreakdown: Record<TransactionCategory, number>,
    monthlyIncome: number,
    months: number
  ): BudgetRecommendation[] {
    const recommendations: BudgetRecommendation[] = [];
    
    // South African budget guidelines (percentages of income)
    const budgetGuidelines: Record<TransactionCategory, { min: number; max: number; ideal: number }> = {
      groceries: { min: 0.10, max: 0.15, ideal: 0.12 },
      transport: { min: 0.10, max: 0.20, ideal: 0.15 },
      utilities: { min: 0.05, max: 0.10, ideal: 0.08 },
      healthcare: { min: 0.05, max: 0.10, ideal: 0.07 },
      insurance: { min: 0.05, max: 0.10, ideal: 0.08 },
      entertainment: { min: 0.02, max: 0.08, ideal: 0.05 },
      dining: { min: 0.02, max: 0.08, ideal: 0.05 },
      shopping: { min: 0.02, max: 0.10, ideal: 0.06 },
      education: { min: 0.02, max: 0.15, ideal: 0.05 },
      travel: { min: 0.01, max: 0.10, ideal: 0.03 },
      investments: { min: 0.10, max: 0.25, ideal: 0.15 },
      income: { min: 0, max: 0, ideal: 0 },
      transfers: { min: 0, max: 0.05, ideal: 0.02 },
      fees: { min: 0.01, max: 0.03, ideal: 0.02 },
      other: { min: 0, max: 0.05, ideal: 0.02 }
    };

    for (const [category, guideline] of Object.entries(budgetGuidelines)) {
      const cat = category as TransactionCategory;
      const currentMonthlySpending = (categoryBreakdown[cat] || 0) / months;
      const currentPercentage = monthlyIncome > 0 ? currentMonthlySpending / monthlyIncome : 0;
      
      const suggestedAmount = monthlyIncome * guideline.ideal;
      const isOverspending = currentPercentage > guideline.max;
      const isUnderspending = currentPercentage < guideline.min && this.ESSENTIAL_CATEGORIES.includes(cat);
      
      let confidence = 0.8;
      let reasoning = '';
      let priority: 'high' | 'medium' | 'low' = 'medium';
      const costCuttingTips: string[] = [];
      
      if (isOverspending) {
        priority = 'high';
        confidence = 0.9;
        reasoning = `You're spending ${(currentPercentage * 100).toFixed(1)}% of income on ${category}, which exceeds the recommended ${(guideline.max * 100).toFixed(1)}% maximum.`;
        costCuttingTips.push(...this.getCostCuttingTips(cat));
      } else if (isUnderspending) {
        priority = 'medium';
        reasoning = `Consider allocating more to ${category} for better financial health.`;
      } else {
        priority = 'low';
        reasoning = `Your ${category} spending is within healthy limits.`;
      }
      
      if (currentMonthlySpending > 0 || this.ESSENTIAL_CATEGORIES.includes(cat)) {
        recommendations.push({
          category: cat,
          suggestedAmount,
          currentSpending: currentMonthlySpending,
          confidence,
          reasoning,
          costCuttingTips,
          priority
        });
      }
    }
    
    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  private static getCostCuttingTips(category: TransactionCategory): string[] {
    const tips: Record<TransactionCategory, string[]> = {
      groceries: [
        'Plan meals in advance and create shopping lists',
        'Buy generic brands instead of name brands',
        'Shop at discount stores like Shoprite or Checkers',
        'Use loyalty programs and collect points',
        'Buy in bulk for non-perishable items'
      ],
      transport: [
        'Use public transport or carpooling when possible',
        'Consider ride-sharing apps for occasional trips',
        'Walk or cycle for short distances',
        'Maintain your vehicle regularly to improve fuel efficiency',
        'Compare fuel prices and use apps like AA to find cheaper stations'
      ],
      entertainment: [
        'Look for free events and activities in your area',
        'Share streaming subscriptions with family',
        'Take advantage of happy hour specials',
        'Use discount vouchers and group deals',
        'Consider home entertainment instead of going out'
      ],
      dining: [
        'Cook more meals at home',
        'Pack lunch for work instead of buying',
        'Use restaurant specials and promotions',
        'Limit expensive coffee shop visits',
        'Try local, affordable restaurants instead of chains'
      ],
      utilities: [
        'Switch to energy-efficient appliances',
        'Use prepaid electricity to monitor usage',
        'Install solar geysers or panels if possible',
        'Fix leaks promptly to avoid water waste',
        'Use energy-saving light bulbs'
      ],
      shopping: [
        'Wait 24 hours before making non-essential purchases',
        'Compare prices online before buying',
        'Shop during sales and use discount codes',
        'Buy second-hand items when appropriate',
        'Set a monthly shopping budget and stick to it'
      ],
      healthcare: [
        'Use generic medications when available',
        'Take advantage of medical aid benefits',
        'Consider clinic visits for minor issues',
        'Maintain preventive care to avoid costly treatments',
        'Compare prices for medical procedures'
      ],
      insurance: [
        'Review and compare insurance policies annually',
        'Increase deductibles to lower premiums',
        'Bundle policies with the same provider',
        'Maintain a good credit score for better rates',
        'Consider usage-based insurance for vehicles'
      ],
      education: [
        'Look for online courses and free resources',
        'Apply for bursaries and scholarships',
        'Buy used textbooks or rent them',
        'Use library resources and study groups',
        'Consider part-time or distance learning options'
      ],
      travel: [
        'Book flights and accommodation in advance',
        'Travel during off-peak seasons',
        'Use travel reward programs and points',
        'Consider local destinations to reduce costs',
        'Look for package deals and group discounts'
      ],
      investments: [
        'Start with low-cost index funds',
        'Use tax-free savings accounts (TFSA)',
        'Consider employer retirement matching',
        'Automate investments to build discipline',
        'Diversify across different asset classes'
      ],
      income: [],
      transfers: [
        'Minimize unnecessary money transfers',
        'Use free transfer options when available',
        'Consolidate transfers to reduce fees'
      ],
      fees: [
        'Choose bank accounts with lower fees',
        'Use your bank\'s ATMs to avoid charges',
        'Maintain minimum balances to waive fees',
        'Review and negotiate bank charges'
      ],
      other: [
        'Track and categorize all expenses',
        'Review subscriptions and cancel unused ones',
        'Set up automatic savings transfers'
      ]
    };
    
    return tips[category] || [];
  }

  private static generateInsights(
    transactions: Transaction[],
    monthlyIncome: number,
    monthlyExpenses: number,
    savingsRate: number
  ): string[] {
    const insights: string[] = [];
    
    if (savingsRate < 0.1) {
      insights.push('Your savings rate is below 10%. Consider reducing discretionary spending to build an emergency fund.');
    } else if (savingsRate > 0.25) {
      insights.push('Excellent savings rate! You\'re saving over 25% of your income.');
    }
    
    if (monthlyIncome > 0) {
      const expenseRatio = monthlyExpenses / monthlyIncome;
      if (expenseRatio > 0.9) {
        insights.push('You\'re spending over 90% of your income. Focus on increasing income or reducing expenses.');
      }
    }
    
    // Analyze spending patterns
    const recentTransactions = transactions
      .filter(t => Date.now() - t.date < 30 * 24 * 60 * 60 * 1000) // Last 30 days
      .filter(t => t.type === 'debit');
    
    const categorySpending = recentTransactions.reduce((acc, t) => {
      acc[t.category] = (acc[t.category] || 0) + t.amount;
      return acc;
    }, {} as Record<TransactionCategory, number>);
    
    const topCategory = Object.entries(categorySpending)
      .sort(([,a], [,b]) => b - a)[0];
    
    if (topCategory) {
      insights.push(`Your highest spending category this month is ${topCategory[0]} (R${topCategory[1].toFixed(2)}).`);
    }
    
    // Check for recurring subscriptions
    const subscriptions = transactions.filter(t => 
      t.isRecurring && 
      this.DISCRETIONARY_CATEGORIES.includes(t.category) &&
      t.amount < 500
    );
    
    if (subscriptions.length > 3) {
      const totalSubscriptions = subscriptions.reduce((sum, t) => sum + t.amount, 0);
      insights.push(`You have ${subscriptions.length} recurring subscriptions totaling R${totalSubscriptions.toFixed(2)}/month. Review if all are necessary.`);
    }
    
    return insights;
  }

  private static identifyRiskFactors(
    transactions: Transaction[],
    analysis: ReturnType<typeof TransactionCategorizer.analyzeSpendingPatterns>
  ): string[] {
    const risks: string[] = [];
    
    // Check for irregular income
    const incomeTransactions = transactions.filter(t => t.category === 'income');
    if (incomeTransactions.length > 0) {
      const incomeAmounts = incomeTransactions.map(t => t.amount);
      const avgIncome = incomeAmounts.reduce((sum, amount) => sum + amount, 0) / incomeAmounts.length;
      const incomeVariability = Math.sqrt(
        incomeAmounts.reduce((sum, amount) => sum + Math.pow(amount - avgIncome, 2), 0) / incomeAmounts.length
      ) / avgIncome;
      
      if (incomeVariability > 0.3) {
        risks.push('Your income shows high variability. Consider building a larger emergency fund.');
      }
    }
    
    // Check for high discretionary spending
    const totalExpenses = Object.values(analysis.categoryBreakdown).reduce((sum, amount) => sum + amount, 0);
    const discretionarySpending = this.DISCRETIONARY_CATEGORIES.reduce(
      (sum, cat) => sum + (analysis.categoryBreakdown[cat] || 0), 0
    );
    
    if (discretionarySpending / totalExpenses > 0.4) {
      risks.push('Over 40% of your spending is discretionary. This could impact your ability to handle emergencies.');
    }
    
    // Check for increasing spending trends
    const recentMonthSpending = transactions
      .filter(t => Date.now() - t.date < 30 * 24 * 60 * 60 * 1000)
      .filter(t => t.type === 'debit')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const previousMonthSpending = transactions
      .filter(t => {
        const daysDiff = (Date.now() - t.date) / (24 * 60 * 60 * 1000);
        return daysDiff >= 30 && daysDiff < 60;
      })
      .filter(t => t.type === 'debit')
      .reduce((sum, t) => sum + t.amount, 0);
    
    if (previousMonthSpending > 0 && recentMonthSpending > previousMonthSpending * 1.2) {
      risks.push('Your spending has increased by over 20% compared to last month. Monitor this trend closely.');
    }
    
    return risks;
  }

  static generateSmartBudget(
    monthlyIncome: number,
    existingTransactions: Transaction[] = [],
    goals: { emergencyFund?: number; savingsGoal?: number } = {}
  ): Budget[] {
    const budgets: Omit<Budget, 'id' | 'userId' | 'createdAt' | 'updatedAt'>[] = [];
    const now = Date.now();
    const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1).getTime();
    const endOfMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getTime();
    
    // Essential budgets
    const essentialBudgets = [
      { category: 'groceries' as const, percentage: 0.12, name: 'Groceries' },
      { category: 'transport' as const, percentage: 0.15, name: 'Transport' },
      { category: 'utilities' as const, percentage: 0.08, name: 'Utilities' },
      { category: 'healthcare' as const, percentage: 0.07, name: 'Healthcare' },
      { category: 'insurance' as const, percentage: 0.08, name: 'Insurance' }
    ];
    
    // Discretionary budgets
    const discretionaryBudgets = [
      { category: 'entertainment' as const, percentage: 0.05, name: 'Entertainment' },
      { category: 'dining' as const, percentage: 0.05, name: 'Dining Out' },
      { category: 'shopping' as const, percentage: 0.06, name: 'Shopping' }
    ];
    
    [...essentialBudgets, ...discretionaryBudgets].forEach(budget => {
      budgets.push({
        name: budget.name,
        category: budget.category,
        amount: monthlyIncome * budget.percentage,
        spent: 0,
        period: 'monthly',
        startDate: startOfMonth,
        endDate: endOfMonth,
        isActive: true,
        alerts: {
          enabled: true,
          threshold: 80
        }
      });
    });
    
    return budgets as Budget[];
  }
}
