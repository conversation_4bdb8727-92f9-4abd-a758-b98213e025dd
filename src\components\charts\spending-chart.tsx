"use client";

import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

// Using Recharts' built-in type for Pie label props
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";

interface SpendingData {
  category: string;
  amount: number;
  color?: string;
}

interface SpendingChartProps {
  data: SpendingData[];
  type?: 'pie' | 'bar';
  title?: string;
}

const COLORS = [
  '#3B82F6', // blue
  '#EF4444', // red
  '#10B981', // green
  '#F59E0B', // yellow
  '#8B5CF6', // purple
  '#EC4899', // pink
  '#06B6D4', // cyan
  '#84CC16', // lime
  '#F97316', // orange
  '#6B7280', // gray
];

const categoryLabels: Record<string, string> = {
  groceries: 'Groceries',
  transport: 'Transport',
  entertainment: 'Entertainment',
  utilities: 'Utilities',
  healthcare: 'Healthcare',
  education: 'Education',
  shopping: 'Shopping',
  dining: 'Dining',
  travel: 'Travel',
  insurance: 'Insurance',
  investments: 'Investments',
  income: 'Income',
  transfers: 'Transfers',
  fees: 'Fees',
  other: 'Other',
};

export function SpendingChart({ data, type = 'pie', title = 'Spending by Category' }: SpendingChartProps) {
  const chartData = data.map((item, index) => ({
    ...item,
    name: categoryLabels[item.category] || item.category,
    color: item.color || COLORS[index % COLORS.length],
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{payload[0].payload.name}</p>
          <p className="text-blue-600">
            Amount: {formatCurrency(payload[0].value)}
          </p>
        </div>
      );
    }
    return null;
  };

  const renderPieChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={{
            content: (props: any) => {
              const { cx, cy, midAngle, innerRadius, outerRadius, percent, index, name } = props;
              const RADIAN = Math.PI / 180;
              const radius = innerRadius + (outerRadius - innerRadius) * 1.3;
              const x = cx + radius * Math.cos(-midAngle * RADIAN);
              const y = cy + radius * Math.sin(-midAngle * RADIAN);
              
              return (
                <text
                  x={x}
                  y={y}
                  fill={chartData[index]?.color || '#000'}
                  textAnchor={x > cx ? 'start' : 'end'}
                  dominantBaseline="central"
                >
                  {`${name} ${(percent * 100).toFixed(0)}%`}
                </text>
              );
            }
          }}
          labelLine={false}
          outerRadius={80}
          fill="#8884d8"
          dataKey="amount"
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
      </PieChart>
    </ResponsiveContainer>
  );

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="name" 
          angle={-45}
          textAnchor="end"
          height={80}
          fontSize={12}
        />
        <YAxis 
          tickFormatter={(value) => formatCurrency(value).replace('R ', 'R')}
          fontSize={12}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey="amount" fill="#3B82F6" />
      </BarChart>
    </ResponsiveContainer>
  );

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-gray-500">
            No spending data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {type === 'pie' ? renderPieChart() : renderBarChart()}
        
        {/* Legend for pie chart */}
        {type === 'pie' && (
          <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
            {chartData.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-gray-600">{item.name}</span>
                <span className="font-medium ml-auto">
                  {formatCurrency(item.amount)}
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
