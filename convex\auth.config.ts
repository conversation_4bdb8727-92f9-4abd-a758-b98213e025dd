// convex/auth.config.ts
// This file is not needed in Convex v1.27.0+ with Clerk
// Authentication is now configured through the ConvexProviderWithClerk component
// and the Clerk dashboard

export default {
  // This file is kept for backward compatibility
  // All authentication configuration is now done through the Clerk dashboard
  // and the ConvexProviderWithClerk component in your frontend code
};