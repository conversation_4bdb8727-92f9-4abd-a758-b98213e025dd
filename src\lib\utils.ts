import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number | undefined | null, currency: string = 'ZAR'): string {
  if (amount === undefined || amount === null || isNaN(Number(amount))) {
    return 'R 0.00';
  }
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(Number(amount));
}

/**
 * Formats a date in a human-readable format
 * @param date - Date object or timestamp to format
 * @returns Formatted date string (e.g., "15 Jan 2023")
 */
export function formatDate(date: Date | number | string): string {
  const d = new Date(date);
  return new Intl.DateTimeFormat('en-US', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  }).format(d);
}
