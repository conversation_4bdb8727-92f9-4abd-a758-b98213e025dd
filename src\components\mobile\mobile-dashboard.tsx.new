"use client";

import { useEffect, useState } from 'react';
import { cn, formatCurrency } from '@/lib/utils';
import { MobileCard, TouchButton } from '@/components/layout/mobile-nav';
import { MobileTransactionCard } from './mobile-transaction-card';
import { useTransactions, useBudgets, useBankAccounts } from '@/hooks/useRealtime';
import {
  Eye,
  EyeOff,
  TrendingUp,
  TrendingDown,
  Plus,
  ArrowUpRight,
  ArrowDownRight,
  CreditCard,
  PiggyBank,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Brain,
  BarChart3,
  Bell,
  Scan,
  Send,
  Wallet,
  RefreshCw,
} from 'lucide-react';

interface MobileDashboardProps {
  className?: string;
}

export function MobileDashboard({ className }: MobileDashboardProps) {
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  const { transactionsWithBalance } = useTransactions();
  const { budgetProgress } = useBudgets();
  const { accounts, totalBalance, error: bankError, isLoading: isBankAccountsLoading } = useBankAccounts();

  const recentTransactions = transactionsWithBalance?.transactions?.slice(0, 5) || [];
  const currentBalance = totalBalance || 0;
  const isLoading = isBankAccountsLoading || !transactionsWithBalance;

  // Log any errors from the bank accounts hook
  useEffect(() => {
    if (bankError) {
      console.error('Bank accounts error:', bankError);
    }
  }, [bankError]);
  
  // Calculate spending this month
  const thisMonth = new Date();
  const startOfMonth = new Date(thisMonth.getFullYear(), thisMonth.getMonth(), 1);
  const monthlySpending = recentTransactions
    .filter(t => t.type === 'debit' && new Date(t.date) >= startOfMonth)
    .reduce((sum, t) => sum + Math.abs(t.amount), 0);

  const monthlyIncome = recentTransactions
    .filter(t => t.type === 'credit' && new Date(t.date) >= startOfMonth)
    .reduce((sum, t) => sum + t.amount, 0);

  // Budget alerts
  const budgetAlerts = budgetProgress.filter(b => b.isOverBudget || b.isNearLimit);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => setRefreshing(false), 1000);
  };

  const quickActions = [
    { 
      icon: Plus, 
      label: 'Add Transaction', 
      variant: 'primary' as const,
      action: () => window.location.href = '/dashboard/transactions/add'
    },
    { 
      icon: Scan, 
      label: 'Scan Receipt', 
      variant: 'secondary' as const,
      action: () => window.location.href = '/dashboard/scan-receipt'
    },
    { 
      icon: Send, 
      label: 'Send Money', 
      variant: 'primary' as const,
      action: () => window.location.href = '/dashboard/send-money'
    },
    { 
      icon: Wallet, 
      label: 'Pay Bill', 
      variant: 'secondary' as const,
      action: () => window.location.href = '/dashboard/pay-bill'
    }
  ];

  // Show loading state while data is being fetched
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-gray-600">Loading your financial data...</p>
      </div>
    );
  }

  // Show error state if there was an error loading bank accounts
  if (bankError) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading bank accounts</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>We couldn't load your bank account information. Please try refreshing the page.</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => window.location.reload()}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <RefreshCw className="-ml-0.5 mr-2 h-4 w-4" />
                  Refresh page
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Balance */}
      <MobileCard className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-lg font-semibold">Good morning! 👋</h1>
            <p className="text-blue-100 text-sm">Here's your financial overview</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-colors"
          >
            <RefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>

        <div className="space-y-4">
          {/* Total Balance */}
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-blue-100 text-sm">Total Balance</span>
              <button
                onClick={() => setBalanceVisible(!balanceVisible)}
                className="p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
                aria-label={balanceVisible ? 'Hide balance' : 'Show balance'}
              >
                {balanceVisible ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
            <div className="text-3xl font-bold min-h-[2.25rem] flex items-center">
              {balanceVisible ? (
                <span className={cn(
                  'transition-opacity duration-200',
                  currentBalance === 0 ? 'opacity-70' : 'opacity-100'
                )}>
                  {formatCurrency(currentBalance)}
                </span>
              ) : (
                <span className="tracking-widest">••••••</span>
              )}
            </div>
            {currentBalance === 0 && (
              <p className="text-xs text-blue-200 mt-1">
                Add bank accounts to see your total balance
              </p>
            )}
          </div>

          {/* Monthly Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white bg-opacity-10 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-1">
                <ArrowDownRight className="w-4 h-4 text-red-300" />
                <span className="text-xs text-blue-100">This Month</span>
              </div>
              <div className="text-lg font-semibold">
                {balanceVisible ? (
                  monthlySpending > 0 ? (
                    formatCurrency(monthlySpending)
                  ) : (
                    <span className="opacity-70">R0.00</span>
                  )
                ) : (
                  '••••'
                )}
              </div>
              <div className="text-xs text-blue-100">Spent</div>
              {recentTransactions.length === 0 && balanceVisible && (
                <div className="text-2xs text-blue-200 mt-1">No spending yet</div>
              )}
            </div>
            
            <div className="bg-white bg-opacity-10 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-1">
                <ArrowUpRight className="w-4 h-4 text-green-300" />
                <span className="text-xs text-blue-100">This Month</span>
              </div>
              <div className="text-lg font-semibold">
                {balanceVisible ? (
                  monthlyIncome > 0 ? (
                    formatCurrency(monthlyIncome)
                  ) : (
                    <span className="opacity-70">R0.00</span>
                  )
                ) : (
                  '••••'
                )}
              </div>
              <div className="text-xs text-blue-100">Earned</div>
              {recentTransactions.filter(t => t.type === 'credit').length === 0 && balanceVisible && (
                <div className="text-2xs text-blue-200 mt-1">No income yet</div>
              )}
            </div>
          </div>
        </div>
      </MobileCard>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action, index) => (
            <TouchButton
              key={index}
              onClick={action.action}
              variant={action.variant}
              className="flex flex-col items-center justify-center p-4 h-full min-h-[100px]"
            >
              <action.icon className="w-6 h-6 mb-2" />
              <span className="text-sm font-medium">{action.label}</span>
            </TouchButton>
          ))}
        </div>
      </div>

      {/* Budget Alerts */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Budget Alerts</h2>
          <TouchButton 
            size="sm" 
            variant="ghost"
            onClick={() => window.location.href = '/dashboard/budgets'}
          >
            {budgetAlerts.length > 0 ? 'View All' : 'Set Up'}
          </TouchButton>
        </div>
        
        {budgetAlerts.length > 0 ? (
          <div className="space-y-3">
            {budgetAlerts.slice(0, 2).map((budget) => (
              <MobileCard key={budget._id} className="border-l-4 border-l-orange-500">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">{budget.name}</h3>
                    <p className="text-sm text-gray-600">
                      {budget.spentPercentage}% of {formatCurrency(budget.amount)} spent
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-orange-600">
                      {formatCurrency(budget.amount - budget.spent)} left
                    </div>
                    <div className="text-xs text-gray-500">
                      {budget.remainingDays} days remaining
                    </div>
                  </div>
                </div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      budget.spentPercentage > 90 ? 'bg-red-500' : 
                      budget.spentPercentage > 70 ? 'bg-orange-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(budget.spentPercentage, 100)}%` }}
                  />
                </div>
              </MobileCard>
            ))}
          </div>
        ) : (
          <MobileCard className="text-center py-6">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Target className="w-6 h-6 text-orange-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No active budgets</h3>
            <p className="text-gray-600 mb-4">Create a budget to track your spending and save money.</p>
            <TouchButton 
              onClick={() => window.location.href = '/dashboard/budgets/new'}
              className="w-full"
            >
              Create Budget
            </TouchButton>
          </MobileCard>
        )}
      </div>

      {/* Accounts */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Accounts</h2>
          <div className="flex space-x-2">
            <TouchButton 
              size="sm" 
              variant="secondary"
              onClick={() => window.location.href = '/dashboard/bank-accounts'}
            >
              View All
            </TouchButton>
            <TouchButton 
              size="sm" 
              variant="primary"
              onClick={() => window.location.href = '/dashboard/bank-accounts/add'}
            >
              <Plus className="w-4 h-4 mr-1" /> Add
            </TouchButton>
          </div>
        </div>
        
        {accounts.length === 0 ? (
          <MobileCard className="text-center py-8">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CreditCard className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No accounts yet</h3>
            <p className="text-gray-600 mb-4">Add your bank accounts to get started with tracking your finances.</p>
            <TouchButton 
              onClick={() => window.location.href = '/dashboard/bank-accounts/add'}
              className="w-full"
            >
              Add Bank Account
            </TouchButton>
          </MobileCard>
        ) : (
          <div className="space-y-3">
            {accounts.slice(0, 2).map((account) => (
              <MobileCard key={account._id}>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <CreditCard className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">{account.accountName}</h3>
                    <p className="text-sm text-gray-600 capitalize">{account.accountType}</p>
                  </div>
                  <div className="text-right flex-shrink-0 ml-2">
                    <div className="font-semibold text-gray-900">
                      {balanceVisible ? formatCurrency(account.balance) : '••••••'}
                    </div>
                    <div className="text-xs text-gray-500">{account.currency}</div>
                  </div>
                </div>
              </MobileCard>
            ))}
          </div>
        )}
      </div>

      {/* Recent Transactions */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Recent Transactions</h2>
          <div className="flex space-x-2">
            <TouchButton 
              size="sm" 
              variant="secondary"
              onClick={() => window.location.href = '/dashboard/transactions'}
            >
              View All
            </TouchButton>
            <TouchButton 
              size="sm" 
              variant="primary"
              onClick={() => window.location.href = '/dashboard/transactions/add'}
            >
              <Plus className="w-4 h-4 mr-1" /> Add
            </TouchButton>
          </div>
        </div>
        <div className="space-y-3">
          {recentTransactions.length > 0 ? (
            recentTransactions.map((transaction) => (
              <MobileTransactionCard
                key={transaction._id}
                transaction={transaction}
              />
            ))
          ) : (
            <MobileCard className="text-center py-8">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CreditCard className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
              <p className="text-gray-600 mb-4">Add your first transaction to start tracking your spending</p>
              <div className="flex flex-col space-y-2">
                <TouchButton 
                  onClick={() => window.location.href = '/dashboard/transactions/add'}
                  className="w-full"
                >
                  Add Transaction
                </TouchButton>
                <TouchButton 
                  onClick={() => window.location.href = '/dashboard/bank-accounts'}
                  variant="outline"
                  className="w-full"
                >
                  Connect Bank Account
                </TouchButton>
              </div>
            </MobileCard>
          )}
        </div>
      </div>
    </div>
  );
}
