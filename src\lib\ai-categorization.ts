import { TransactionCategory } from './types';

// AI-powered transaction categorization service
export class TransactionCategorizer {
  private static patterns: Record<TransactionCategory, string[]> = {
    groceries: [
      'shoprite', 'pick n pay', 'checkers', 'woolworths', 'spar', 'food lover',
      'makro', 'game', 'cambridge food', 'superspar', 'kwikspar',
      'grocery', 'supermarket', 'fresh produce', 'butchery'
    ],
    transport: [
      'uber', 'bolt', 'taxify', 'gautrain', 'metrobus', 'rea vaya',
      'petrol', 'fuel', 'bp', 'shell', 'engen', 'sasol', 'caltex',
      'car wash', 'parking', 'toll', 'e-toll', 'sanral',
      'aa', 'automobile association', 'car service', 'mechanic'
    ],
    utilities: [
      'eskom', 'city power', 'city of cape town', 'ethekwini',
      'municipal', 'water', 'electricity', 'rates', 'sewerage',
      'refuse', 'prepaid electricity', 'prepaid water'
    ],
    entertainment: [
      'netflix', 'dstv', 'showmax', 'amazon prime', 'spotify',
      'apple music', 'youtube premium', 'cinema', 'movies',
      'ster kinekor', 'nu metro', 'theatre', 'concert',
      'gaming', 'playstation', 'xbox', 'steam'
    ],
    dining: [
      'mcdonald', 'kfc', 'burger king', 'steers', 'nando',
      'wimpy', 'spur', 'ocean basket', 'panarottis',
      'restaurant', 'cafe', 'coffee shop', 'mugg & bean',
      'vida e caffe', 'seattle coffee', 'starbucks'
    ],
    healthcare: [
      'pharmacy', 'clicks', 'dischem', 'medirite', 'alpha pharm',
      'doctor', 'dentist', 'hospital', 'clinic', 'medical',
      'optometrist', 'physiotherapy', 'psychologist',
      'medical aid', 'discovery', 'momentum', 'bonitas'
    ],
    education: [
      'school', 'university', 'college', 'tuition', 'fees',
      'books', 'stationery', 'exam', 'course', 'training',
      'unisa', 'wits', 'uct', 'stellenbosch', 'up'
    ],
    shopping: [
      'takealot', 'amazon', 'ebay', 'clothing', 'fashion',
      'mr price', 'edgars', 'truworths', 'foschini',
      'pep', 'ackermans', 'jet', 'sportsmans warehouse'
    ],
    insurance: [
      'insurance', 'assurance', 'santam', 'outsurance',
      'budget insurance', 'hollard', 'momentum',
      'car insurance', 'home insurance', 'life insurance'
    ],
    investments: [
      'investment', 'unit trust', 'etf', 'shares', 'stocks',
      'retirement annuity', 'ra', 'tfsa', 'easy equities',
      'standard bank securities', 'absa stockbrokers'
    ],
    income: [
      'salary', 'wage', 'bonus', 'commission', 'dividend',
      'interest', 'refund', 'cashback', 'reward'
    ],
    transfers: [
      'transfer', 'payment', 'eft', 'instant money',
      'send money', 'receive money', 'family transfer'
    ],
    fees: [
      'bank fee', 'service fee', 'monthly fee', 'transaction fee',
      'atm fee', 'overdraft', 'interest charge'
    ],
    travel: [
      'hotel', 'accommodation', 'flight', 'airline',
      'kulula', 'mango', 'british airways', 'emirates',
      'booking.com', 'agoda', 'airbnb', 'travel'
    ],
    other: []
  };

  private static southAfricanMerchants = [
    'shoprite', 'pick n pay', 'checkers', 'woolworths', 'spar',
    'uber', 'bolt', 'eskom', 'dstv', 'netflix', 'clicks',
    'dischem', 'takealot', 'mr price', 'edgars'
  ];

  static categorize(description: string, merchant?: string, amount?: number): TransactionCategory {
    const text = `${description} ${merchant || ''}`.toLowerCase();
    
    // Check for exact merchant matches first
    if (merchant) {
      const merchantLower = merchant.toLowerCase();
      for (const [category, patterns] of Object.entries(this.patterns)) {
        if (patterns.some(pattern => merchantLower.includes(pattern))) {
          return category as TransactionCategory;
        }
      }
    }

    // Check description patterns
    for (const [category, patterns] of Object.entries(this.patterns)) {
      if (patterns.some(pattern => text.includes(pattern))) {
        return category as TransactionCategory;
      }
    }

    // Amount-based heuristics for South African context
    if (amount) {
      // Large amounts are likely salary/income
      if (amount > 10000 && text.includes('deposit')) {
        return 'income';
      }
      
      // Small regular amounts might be subscriptions
      if (amount < 500 && text.includes('monthly')) {
        return 'entertainment';
      }
      
      // Medium amounts at known grocery stores
      if (amount > 200 && amount < 2000) {
        const groceryKeywords = ['shop', 'market', 'store'];
        if (groceryKeywords.some(keyword => text.includes(keyword))) {
          return 'groceries';
        }
      }
    }

    // Default fallback
    return 'other';
  }

  static getConfidence(description: string, merchant?: string, category: TransactionCategory): number {
    const text = `${description} ${merchant || ''}`.toLowerCase();
    const patterns = this.patterns[category];
    
    if (!patterns || patterns.length === 0) return 0.1;
    
    let matches = 0;
    let totalPatterns = patterns.length;
    
    for (const pattern of patterns) {
      if (text.includes(pattern)) {
        matches++;
      }
    }
    
    // Base confidence on pattern matches
    let confidence = matches / totalPatterns;
    
    // Boost confidence for exact merchant matches
    if (merchant && patterns.some(pattern => merchant.toLowerCase() === pattern)) {
      confidence = Math.min(confidence + 0.3, 1.0);
    }
    
    // Boost confidence for South African merchants
    if (merchant && this.southAfricanMerchants.some(m => merchant.toLowerCase().includes(m))) {
      confidence = Math.min(confidence + 0.2, 1.0);
    }
    
    return Math.max(confidence, 0.1);
  }

  static suggestAlternativeCategories(description: string, merchant?: string): Array<{category: TransactionCategory, confidence: number}> {
    const suggestions: Array<{category: TransactionCategory, confidence: number}> = [];
    
    for (const category of Object.keys(this.patterns) as TransactionCategory[]) {
      const confidence = this.getConfidence(description, merchant, category);
      if (confidence > 0.1) {
        suggestions.push({ category, confidence });
      }
    }
    
    return suggestions
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3);
  }

  static learnFromUserCorrection(
    description: string, 
    merchant: string | undefined, 
    originalCategory: TransactionCategory, 
    correctedCategory: TransactionCategory
  ): void {
    // In a real implementation, this would update the ML model
    // For now, we'll just log the correction for future improvements
    console.log('Learning from correction:', {
      description,
      merchant,
      originalCategory,
      correctedCategory,
      timestamp: new Date().toISOString()
    });
    
    // Could store these corrections in a database for model retraining
  }

  static analyzeSpendingPatterns(transactions: Array<{
    description: string;
    merchant?: string;
    amount: number;
    date: number;
    category: TransactionCategory;
  }>): {
    categoryBreakdown: Record<TransactionCategory, number>;
    monthlyTrends: Array<{month: string, amount: number}>;
    recurringTransactions: Array<{description: string, frequency: string, amount: number}>;
  } {
    const categoryBreakdown: Record<TransactionCategory, number> = {} as any;
    const monthlyData: Record<string, number> = {};
    const recurringMap: Record<string, Array<{date: number, amount: number}>> = {};
    
    transactions.forEach(transaction => {
      // Category breakdown
      if (!categoryBreakdown[transaction.category]) {
        categoryBreakdown[transaction.category] = 0;
      }
      categoryBreakdown[transaction.category] += Math.abs(transaction.amount);
      
      // Monthly trends
      const monthKey = new Date(transaction.date).toISOString().slice(0, 7);
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = 0;
      }
      monthlyData[monthKey] += Math.abs(transaction.amount);
      
      // Recurring transactions
      const key = `${transaction.description}-${transaction.merchant || ''}`;
      if (!recurringMap[key]) {
        recurringMap[key] = [];
      }
      recurringMap[key].push({ date: transaction.date, amount: transaction.amount });
    });
    
    // Analyze recurring patterns
    const recurringTransactions = Object.entries(recurringMap)
      .filter(([_, occurrences]) => occurrences.length >= 2)
      .map(([key, occurrences]) => {
        const avgAmount = occurrences.reduce((sum, o) => sum + Math.abs(o.amount), 0) / occurrences.length;
        const dates = occurrences.map(o => o.date).sort();
        const intervals = [];
        
        for (let i = 1; i < dates.length; i++) {
          intervals.push(dates[i] - dates[i-1]);
        }
        
        const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
        const days = avgInterval / (1000 * 60 * 60 * 24);
        
        let frequency = 'irregular';
        if (days >= 28 && days <= 32) frequency = 'monthly';
        else if (days >= 6 && days <= 8) frequency = 'weekly';
        else if (days >= 13 && days <= 15) frequency = 'bi-weekly';
        
        return {
          description: key.split('-')[0],
          frequency,
          amount: avgAmount
        };
      });
    
    const monthlyTrends = Object.entries(monthlyData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, amount]) => ({ month, amount }));
    
    return {
      categoryBreakdown,
      monthlyTrends,
      recurringTransactions
    };
  }
}
