"use client";

import { useQuery, useMutation } from "convex/react";
import { useUser } from "@clerk/nextjs";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useEffect, useState } from "react";

export function useRealtimeData() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const [userId, setUserId] = useState<Id<"users"> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get or create user
  const existingUser = useQuery(
    api.userPreferences.getUserByClerkId,
    user?.id ? { clerkId: user.id } : "skip"
  );

  const createUser = useMutation(api.userPreferences.createOrUpdateUser);

  useEffect(() => {
    if (user && !existingUser && user.id) {
      createUser({
        clerkId: user.id,
        email: user.emailAddresses[0]?.emailAddress || "",
        firstName: user.firstName || "",
        lastName: user.lastName || "",
      }).then((id) => {
        setUserId(id);
      });
    } else if (existingUser) {
      setUserId(existingUser._id);
    }
  }, [user, existingUser, createUser]);

  // Realtime data subscription
  const realtimeData = useQuery(
    api.syncStatus.getRealtimeData,
    userId ? { userId } : "skip"
  );

  // Notifications
  const notifications = useQuery(
    api.notifications.getNotificationsByUser,
    userId ? { userId, limit: 10 } : "skip"
  );

  const unreadCount = useQuery(
    api.notifications.getUnreadCount,
    userId ? { userId } : "skip"
  );

  // User preferences
  const preferences = useQuery(
    api.userPreferences.getUserPreferences,
    userId ? { userId } : "skip"
  );

  // Sync status
  const syncStatuses = useQuery(
    api.syncStatus.getSyncStatusByUser,
    userId ? { userId } : "skip"
  );

  useEffect(() => {
    if (isUserLoaded) {
      setIsLoading(!userId || realtimeData === undefined);
    }
  }, [isUserLoaded, userId, realtimeData]);

  return {
    userId,
    realtimeData,
    notifications,
    unreadCount: unreadCount || 0,
    preferences,
    syncStatuses,
    isLoading,
  };
}

export function useNotifications() {
  const { userId } = useRealtimeData();

  const notifications = useQuery(
    api.notifications.getNotificationsByUser,
    userId ? { userId, limit: 50 } : "skip"
  );

  const unreadCount = useQuery(
    api.notifications.getUnreadCount,
    userId ? { userId } : "skip"
  );

  const markAsRead = useMutation(api.notifications.markNotificationAsRead);
  const markAllAsRead = useMutation(api.notifications.markAllNotificationsAsRead);
  const deleteNotification = useMutation(api.notifications.deleteNotification);

  return {
    notifications: notifications || [],
    unreadCount: unreadCount || 0,
    markAsRead,
    markAllAsRead: () => userId && markAllAsRead({ userId }),
    deleteNotification,
  };
}

export function useTransactions() {
  const { userId } = useRealtimeData();

  const realtimeTransactions = useQuery(
    api.transactions.getRealtimeTransactions,
    userId ? { userId, limit: 20 } : "skip"
  );

  const transactionsWithBalance = useQuery(
    api.transactions.getTransactionsWithBalance,
    userId ? { userId, limit: 50 } : "skip"
  );

  const createTransaction = useMutation(api.transactions.createTransaction);
  const bulkCreateTransactions = useMutation(api.transactions.bulkCreateTransactions);

  return {
    realtimeTransactions: realtimeTransactions || [],
    transactionsWithBalance,
    createTransaction,
    bulkCreateTransactions,
  };
}

export function useBudgets() {
  const { userId } = useRealtimeData();

  const budgets = useQuery(
    api.budgets.getBudgetsByUser,
    userId ? { userId } : "skip"
  );

  const budgetProgress = useQuery(
    api.budgets.calculateBudgetProgress,
    userId ? { userId } : "skip"
  );

  const budgetAlerts = useQuery(
    api.budgets.getBudgetAlerts,
    userId ? { userId } : "skip"
  );

  const createBudget = useMutation(api.budgets.createBudget);
  const updateBudget = useMutation(api.budgets.updateBudget);
  const deleteBudget = useMutation(api.budgets.deleteBudget);

  return {
    budgets: budgets || [],
    budgetProgress: budgetProgress || [],
    budgetAlerts: budgetAlerts || [],
    createBudget,
    updateBudget,
    deleteBudget,
  };
}

export function useBankAccounts(p0: any) {
  const { user, isLoaded: isUserLoaded, isSignedIn } = useUser();
  const { userId, isLoading: isUserLoading } = useRealtimeData();
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Only fetch accounts if user is authenticated and we have a valid userId
  const shouldFetch = isUserLoaded && isSignedIn && !isUserLoading && !!userId;

  const accounts = useQuery(
    api.bankAccounts.getBankAccountsByUser,
    shouldFetch ? { userId } : "skip"
  );

  const totalBalance = useQuery(
    api.bankAccounts.getTotalBalance,
    shouldFetch ? { userId } : "skip"
  );

  const createAccount = useMutation(api.bankAccounts.createBankAccount)
    .withOptimisticUpdate((localStore, args) => {
      // Add optimistic update logic here if needed
    });
    
  const updateBalance = useMutation(api.bankAccounts.updateBankAccountBalance);
  const deactivateAccount = useMutation(api.bankAccounts.deactivateBankAccount);

  useEffect(() => {
    // Set loading state based on authentication and data fetching status
    if (!isUserLoaded) {
      setIsLoading(true);
      return;
    }

    if (!isSignedIn) {
      setIsLoading(false);
      return;
    }

    // If we're still loading user data or accounts data
    if (isUserLoading || accounts === undefined || totalBalance === undefined) {
      setIsLoading(true);
    } else {
      setIsLoading(false);
    }
  }, [isUserLoading, userId, accounts, totalBalance]);

  // Handle errors
  useEffect(() => {
    if (accounts instanceof Error) {
      setError(accounts);
      console.error('Error fetching accounts:', accounts);
    }
    if (totalBalance instanceof Error) {
      setError(totalBalance);
      console.error('Error fetching total balance:', totalBalance);
    }
  }, [accounts, totalBalance]);

  // Safe access to accounts data
  const safeAccounts = Array.isArray(accounts) ? accounts : [];
  const safeTotalBalance = typeof totalBalance === 'object' && totalBalance && 'total' in totalBalance 
    ? totalBalance.total 
    : 0;

  return {
    accounts: safeAccounts,
    totalBalance: safeTotalBalance,
    error,
    createAccount,
    updateBalance,
    deactivateAccount,
  };
}

export function useUserPreferences() {
  const { userId } = useRealtimeData();

  const preferences = useQuery(
    api.userPreferences.getUserPreferences,
    userId ? { userId } : "skip"
  );

  const updatePreferences = useMutation(api.userPreferences.updateUserPreferences);

  return {
    preferences,
    updatePreferences: (updates: any) => 
      userId && updatePreferences({ userId, ...updates }),
  };
}

export function useActivityLog() {
  const { userId } = useRealtimeData();

  const activityLog = useQuery(
    api.syncStatus.getActivityLog,
    userId ? { userId, limit: 100 } : "skip"
  );

  const logActivity = useMutation(api.syncStatus.logActivity);

  return {
    activityLog: activityLog || [],
    logActivity: (action: string, entityType: string, details?: any) =>
      userId && logActivity({
        userId,
        action,
        entityType,
        details,
      }),
  };
}

// Optimistic updates helper
export function useOptimisticUpdates() {
  const [pendingTransactions, setPendingTransactions] = useState<any[]>([]);
  const [pendingBudgetUpdates, setPendingBudgetUpdates] = useState<any[]>([]);

  const addPendingTransaction = (transaction: any) => {
    const pendingId = `pending-${Date.now()}`;
    setPendingTransactions(prev => [...prev, { ...transaction, id: pendingId, isPending: true }]);
    return pendingId;
  };

  const removePendingTransaction = (pendingId: string) => {
    setPendingTransactions(prev => prev.filter(t => t.id !== pendingId));
  };

  const addPendingBudgetUpdate = (budgetUpdate: any) => {
    const pendingId = `pending-budget-${Date.now()}`;
    setPendingBudgetUpdates(prev => [...prev, { ...budgetUpdate, id: pendingId, isPending: true }]);
    return pendingId;
  };

  const removePendingBudgetUpdate = (pendingId: string) => {
    setPendingBudgetUpdates(prev => prev.filter(b => b.id !== pendingId));
  };

  return {
    pendingTransactions,
    pendingBudgetUpdates,
    addPendingTransaction,
    removePendingTransaction,
    addPendingBudgetUpdate,
    removePendingBudgetUpdate,
  };
}
