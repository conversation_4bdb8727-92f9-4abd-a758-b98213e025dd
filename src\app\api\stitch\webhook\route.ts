import { NextRequest, NextResponse } from 'next/server';
import { stitchAPI, StitchWebhookEvent } from '@/lib/stitch';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../../../../convex/_generated/api';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function POST(request: NextRequest) {
  try {
    const signature = request.headers.get('x-stitch-signature');
    const payload = await request.text();

    if (!signature) {
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    // Verify webhook signature
    const webhookSecret = process.env.STITCH_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('STITCH_WEBHOOK_SECRET not configured');
      return NextResponse.json({ error: 'Webhook not configured' }, { status: 500 });
    }

    const isValid = await stitchAPI.verifyWebhookSignature(payload, signature, webhookSecret);
    if (!isValid) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const event: StitchWebhookEvent = JSON.parse(payload);

    // Process the webhook event
    await processStitchWebhook(event);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function processStitchWebhook(event: StitchWebhookEvent) {
  console.log('Processing Stitch webhook:', event.type, event.id);

  switch (event.type) {
    case 'account.updated':
      await handleAccountUpdated(event);
      break;
    case 'transaction.created':
      await handleTransactionCreated(event);
      break;
    case 'payment.completed':
      await handlePaymentCompleted(event);
      break;
    case 'payment.failed':
      await handlePaymentFailed(event);
      break;
    default:
      console.warn('Unknown webhook event type:', event.type);
  }
}

async function handleAccountUpdated(event: StitchWebhookEvent) {
  const { accountId, balance, currency } = event.data;

  try {
    // Find the account in our database
    const accounts = await convex.query(api.bankAccounts.getBankAccountsByUser, {
      userId: event.data.userId, // This would need to be included in the webhook data
    });

    const account = accounts.find(acc => acc.accountId === accountId);
    if (!account) {
      console.warn('Account not found for webhook update:', accountId);
      return;
    }

    // Update the account balance
    await convex.mutation(api.bankAccounts.updateBankAccountBalance, {
      accountId: account._id,
      balance: balance,
    });

    // Create notification for significant balance changes
    const balanceChange = Math.abs(balance - account.balance);
    if (balanceChange > 1000) { // R1000+ change
      await convex.mutation(api.notifications.createNotification, {
        userId: event.data.userId,
        type: 'transaction_alert',
        title: 'Account Balance Updated',
        message: `Your ${account.accountName} balance has been updated to R${balance.toFixed(2)}`,
        priority: 'medium',
        actionUrl: '/dashboard/bank-accounts',
        metadata: {
          accountId: account._id,
          oldBalance: account.balance,
          newBalance: balance,
          change: balance - account.balance,
        },
      });
    }

    // Log the activity
    await convex.mutation(api.syncStatus.logActivity, {
      userId: event.data.userId,
      action: 'account_balance_updated',
      entityType: 'bankAccount',
      entityId: account._id,
      details: {
        oldBalance: account.balance,
        newBalance: balance,
        source: 'stitch_webhook',
      },
    });

  } catch (error) {
    console.error('Error handling account update webhook:', error);
  }
}

async function handleTransactionCreated(event: StitchWebhookEvent) {
  const { transaction, accountId, userId } = event.data;

  try {
    // Find the account
    const accounts = await convex.query(api.bankAccounts.getBankAccountsByUser, {
      userId: userId,
    });

    const account = accounts.find(acc => acc.accountId === accountId);
    if (!account) {
      console.warn('Account not found for transaction webhook:', accountId);
      return;
    }

    // Check if transaction already exists
    const existingTransactions = await convex.query(api.transactions.getTransactionsByAccount, {
      accountId: account._id,
      limit: 100,
    });

    const exists = existingTransactions.some(t => t.transactionId === transaction.id);
    if (exists) {
      console.log('Transaction already exists:', transaction.id);
      return;
    }

    // Categorize the transaction
    const category = categorizeTransaction(transaction.description, transaction.merchant);

    // Create the transaction
    await convex.mutation(api.transactions.createTransaction, {
      userId: userId,
      accountId: account._id,
      transactionId: transaction.id,
      amount: transaction.amount,
      currency: transaction.currency,
      description: transaction.description,
      category: category as any,
      date: new Date(transaction.date).getTime(),
      type: transaction.type,
      merchant: transaction.merchant,
    });

    // Update sync status
    await convex.mutation(api.syncStatus.updateSyncStatus, {
      userId: userId,
      provider: 'stitch',
      status: 'success',
    });

    console.log('Transaction created from webhook:', transaction.id);

  } catch (error) {
    console.error('Error handling transaction created webhook:', error);
    
    // Update sync status to error
    await convex.mutation(api.syncStatus.updateSyncStatus, {
      userId: event.data.userId,
      provider: 'stitch',
      status: 'error',
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

async function handlePaymentCompleted(event: StitchWebhookEvent) {
  const { paymentId, amount, reference, userId } = event.data;

  try {
    // Create notification for completed payment
    await convex.mutation(api.notifications.createNotification, {
      userId: userId,
      type: 'transaction_alert',
      title: 'Payment Completed',
      message: `Your payment of R${amount.toFixed(2)} (${reference}) has been completed successfully`,
      priority: 'medium',
      actionUrl: '/dashboard/transactions',
      metadata: {
        paymentId,
        amount,
        reference,
        status: 'completed',
      },
    });

    // Log the activity
    await convex.mutation(api.syncStatus.logActivity, {
      userId: userId,
      action: 'payment_completed',
      entityType: 'payment',
      entityId: paymentId,
      details: {
        amount,
        reference,
        source: 'stitch_webhook',
      },
    });

  } catch (error) {
    console.error('Error handling payment completed webhook:', error);
  }
}

async function handlePaymentFailed(event: StitchWebhookEvent) {
  const { paymentId, amount, reference, reason, userId } = event.data;

  try {
    // Create notification for failed payment
    await convex.mutation(api.notifications.createNotification, {
      userId: userId,
      type: 'transaction_alert',
      title: 'Payment Failed',
      message: `Your payment of R${amount.toFixed(2)} (${reference}) has failed: ${reason}`,
      priority: 'high',
      actionUrl: '/dashboard/transactions',
      metadata: {
        paymentId,
        amount,
        reference,
        reason,
        status: 'failed',
      },
    });

    // Log the activity
    await convex.mutation(api.syncStatus.logActivity, {
      userId: userId,
      action: 'payment_failed',
      entityType: 'payment',
      entityId: paymentId,
      details: {
        amount,
        reference,
        reason,
        source: 'stitch_webhook',
      },
    });

  } catch (error) {
    console.error('Error handling payment failed webhook:', error);
  }
}

// Helper function to categorize transactions
function categorizeTransaction(description: string, merchant?: string): string {
  const desc = description.toLowerCase();
  const merch = merchant?.toLowerCase() || '';
  
  // Grocery stores
  if (desc.includes('shoprite') || desc.includes('pick n pay') || desc.includes('checkers') || 
      desc.includes('woolworths') || desc.includes('spar') || merch.includes('grocery')) {
    return 'groceries';
  }
  
  // Transport
  if (desc.includes('uber') || desc.includes('bolt') || desc.includes('taxi') || 
      desc.includes('petrol') || desc.includes('fuel') || desc.includes('gautrain')) {
    return 'transport';
  }
  
  // Utilities
  if (desc.includes('eskom') || desc.includes('city power') || desc.includes('water') || 
      desc.includes('electricity') || desc.includes('municipal')) {
    return 'utilities';
  }
  
  // Entertainment
  if (desc.includes('netflix') || desc.includes('dstv') || desc.includes('cinema') || 
      desc.includes('movie') || desc.includes('showmax')) {
    return 'entertainment';
  }
  
  // Dining
  if (desc.includes('restaurant') || desc.includes('mcdonald') || desc.includes('kfc') || 
      desc.includes('steers') || desc.includes('nando')) {
    return 'dining';
  }
  
  // Healthcare
  if (desc.includes('pharmacy') || desc.includes('clicks') || desc.includes('dischem') || 
      desc.includes('doctor') || desc.includes('medical')) {
    return 'healthcare';
  }
  
  // Default category
  return 'other';
}
