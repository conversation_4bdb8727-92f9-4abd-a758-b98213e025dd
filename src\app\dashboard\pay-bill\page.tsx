"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CalendarIcon, Loader2 } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useToast } from "@/components/ui/use-toast";

// Mock data for payees
const PAYEES = [
  { id: "eskom", name: "Eskom", category: "Utilities" },
  { id: "city-power", name: "City Power", category: "Utilities" },
  { id: "johannesburg-water", name: "Johannesburg Water", category: "Utilities" },
  { id: "mtn", name: "MTN", category: "Mobile" },
  { id: "vodacom", name: "Vodacom", category: "Mobile" },
  { id: "dstv", name: "DStv", category: "Entertainment" },
  { id: "netflix", name: "Netflix", category: "Entertainment" },
  { id: "discovery", name: "Discovery Health", category: "Insurance" },
];

export default function PayBillPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPayee, setSelectedPayee] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [reference, setReference] = useState<string>("");
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [step, setStep] = useState<"select" | "confirm">("select");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Show success toast
      toast({
        title: "Payment Successful",
        description: `Your payment of ZAR ${amount} to ${PAYEES.find(p => p.id === selectedPayee)?.name} has been processed.`,
      });
      
      // Reset form
      setSelectedPayee("");
      setAmount("");
      setReference("");
      setDate(new Date());
      setStep("select");
      
      // Redirect to transactions after a short delay
      setTimeout(() => {
        router.push("/dashboard/transactions");
      }, 2000);
      
    } catch (error) {
      toast({
        title: "Payment Failed",
        description: "There was an error processing your payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const payee = PAYEES.find(p => p.id === selectedPayee);

  if (step === "confirm" && payee) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <h1 className="text-3xl font-bold mb-6">Confirm Payment</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>Review Payment Details</CardTitle>
            <CardDescription>Please confirm the details of your payment</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Payee</p>
                <p className="font-medium">{payee.name}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Category</p>
                <p className="font-medium">{payee.category}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Amount</p>
                <p className="font-medium">ZAR {amount}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Reference</p>
                <p className="font-medium">{reference || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Payment Date</p>
                <p className="font-medium">{date ? format(date, "PPP") : "Immediate"}</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setStep("select")} disabled={isLoading}>
              Back
            </Button>
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Confirm & Pay"
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Pay a Bill</h1>
        <p className="text-muted-foreground">Make payments to your favorite service providers</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Details</CardTitle>
          <CardDescription>Enter the details of your payment</CardDescription>
        </CardHeader>
        <form onSubmit={(e) => {
          e.preventDefault();
          if (selectedPayee && amount) {
            setStep("confirm");
          }
        }}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Select Payee
              </label>
              <Select value={selectedPayee} onValueChange={setSelectedPayee} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select a payee" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(
                    PAYEES.reduce((acc: Record<string, typeof PAYEES>, payee) => {
                      if (!acc[payee.category]) {
                        acc[payee.category] = [];
                      }
                      acc[payee.category].push(payee);
                      return acc;
                    }, {})
                  ).map(([category, payees]) => (
                    <div key={category} className="mb-2">
                      <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
                        {category}
                      </div>
                      {payees.map((payee) => (
                        <SelectItem key={payee.id} value={payee.id}>
                          {payee.name}
                        </SelectItem>
                      ))}
                    </div>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Amount (ZAR)
                </label>
                <Input
                  type="number"
                  min="0.01"
                  step="0.01"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Payment Date
                </label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Reference (Optional)
              </label>
              <Input
                placeholder="e.g., Account number or invoice"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={!selectedPayee || !amount || isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Continue to Payment"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}