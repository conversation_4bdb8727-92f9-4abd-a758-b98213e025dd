import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const createBudget = mutation({
  args: {
    userId: v.id("users"),
    name: v.string(),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    amount: v.number(),
    period: v.union(v.literal("weekly"), v.literal("monthly"), v.literal("yearly")),
    startDate: v.number(),
    endDate: v.number(),
    alertThreshold: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    const budgetId = await ctx.db.insert("budgets", {
      userId: args.userId,
      name: args.name,
      category: args.category,
      amount: args.amount,
      spent: 0,
      period: args.period,
      startDate: args.startDate,
      endDate: args.endDate,
      isActive: true,
      alerts: {
        enabled: args.alertThreshold !== undefined,
        threshold: args.alertThreshold ?? 80,
      },
      createdAt: now,
      updatedAt: now,
    });

    return budgetId;
  },
});

export const getBudgetsByUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("budgets")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();
  },
});

export const updateBudgetSpent = mutation({
  args: {
    budgetId: v.id("budgets"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    const budget = await ctx.db.get(args.budgetId);
    if (!budget) throw new Error("Budget not found");

    await ctx.db.patch(args.budgetId, {
      spent: budget.spent + args.amount,
      updatedAt: Date.now(),
    });
  },
});

export const calculateBudgetProgress = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const budgets = await ctx.db
      .query("budgets")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const results = [];

    for (const budget of budgets) {
      // Get transactions for this budget's category and period
      const transactions = await ctx.db
        .query("transactions")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .filter((q) => 
          q.and(
            q.eq(q.field("category"), budget.category),
            q.eq(q.field("type"), "debit"),
            q.gte(q.field("date"), budget.startDate),
            q.lte(q.field("date"), budget.endDate)
          )
        )
        .collect();

      const actualSpent = transactions.reduce((sum, t) => sum + Math.abs(t.amount), 0);
      const percentage = (actualSpent / budget.amount) * 100;
      const isOverBudget = actualSpent > budget.amount;
      const isNearLimit = percentage >= budget.alerts.threshold;

      results.push({
        ...budget,
        actualSpent,
        percentage: Math.min(percentage, 100),
        isOverBudget,
        isNearLimit: isNearLimit && !isOverBudget,
        remaining: Math.max(budget.amount - actualSpent, 0),
      });
    }

    return results;
  },
});

export const updateBudget = mutation({
  args: {
    budgetId: v.id("budgets"),
    name: v.optional(v.string()),
    amount: v.optional(v.number()),
    alertThreshold: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { budgetId, alertThreshold, ...updates } = args;
    
    const budget = await ctx.db.get(budgetId);
    if (!budget) throw new Error("Budget not found");

    const updateData: any = {
      ...updates,
      updatedAt: Date.now(),
    };

    if (alertThreshold !== undefined) {
      updateData.alerts = {
        ...budget.alerts,
        threshold: alertThreshold,
        enabled: alertThreshold > 0,
      };
    }

    await ctx.db.patch(budgetId, updateData);
  },
});

export const deleteBudget = mutation({
  args: { budgetId: v.id("budgets") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.budgetId, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});



export const getBudgetAlerts = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const budgets = await ctx.db
      .query("budgets")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const results = [];

    for (const budget of budgets) {
      // Get transactions for this budget's category and period
      const transactions = await ctx.db
        .query("transactions")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .filter((q) =>
          q.and(
            q.eq(q.field("category"), budget.category),
            q.eq(q.field("type"), "debit"),
            q.gte(q.field("date"), budget.startDate),
            q.lte(q.field("date"), budget.endDate)
          )
        )
        .collect();

      const actualSpent = transactions.reduce((sum, t) => sum + Math.abs(t.amount), 0);
      const percentage = (actualSpent / budget.amount) * 100;
      const isOverBudget = actualSpent > budget.amount;
      const isNearLimit = percentage >= budget.alerts.threshold;

      if (budget.alerts.enabled && (isNearLimit || isOverBudget)) {
        results.push({
          budgetId: budget._id,
          name: budget.name,
          category: budget.category,
          percentage: Math.min(percentage, 100),
          isOverBudget,
          amount: budget.amount,
          spent: actualSpent,
        });
      }
    }

    return results;
  },
});
