"use client";

import { useState, useCallback } from 'react';
import { StitchAccount, StitchTransaction } from '@/lib/stitch';

interface UseStitchReturn {
  isLoading: boolean;
  error: string | null;
  connectBank: () => Promise<void>;
  fetchAccounts: (token: string) => Promise<StitchAccount[]>;
  fetchTransactions: (token: string, accountId: string) => Promise<StitchTransaction[]>;
}

export function useStitch(): UseStitchReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const connectBank = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/stitch/auth');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get authorization URL');
      }

      // Redirect to Stitch authorization
      window.location.href = data.authUrl;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchAccounts = useCallback(async (token: string): Promise<StitchAccount[]> => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/stitch/accounts?token=${encodeURIComponent(token)}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch accounts');
      }

      return data.accounts;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTransactions = useCallback(async (token: string, accountId: string): Promise<StitchTransaction[]> => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/stitch/accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userToken: token, accountId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch transactions');
      }

      return data.transactions;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    connectBank,
    fetchAccounts,
    fetchTransactions,
  };
}

// Hook for managing Stitch connection state
export function useStitchConnection() {
  const [isConnected, setIsConnected] = useState(false);
  const [userToken, setUserToken] = useState<string | null>(null);
  const [accounts, setAccounts] = useState<StitchAccount[]>([]);

  const stitch = useStitch();

  const connect = useCallback(async () => {
    await stitch.connectBank();
  }, [stitch]);

  const loadAccounts = useCallback(async (token: string) => {
    try {
      const fetchedAccounts = await stitch.fetchAccounts(token);
      setAccounts(fetchedAccounts);
      setUserToken(token);
      setIsConnected(true);
    } catch (error) {
      console.error('Failed to load accounts:', error);
    }
  }, [stitch]);

  const disconnect = useCallback(() => {
    setIsConnected(false);
    setUserToken(null);
    setAccounts([]);
  }, []);

  return {
    isConnected,
    userToken,
    accounts,
    isLoading: stitch.isLoading,
    error: stitch.error,
    connect,
    loadAccounts,
    disconnect,
    fetchTransactions: stitch.fetchTransactions,
  };
}
