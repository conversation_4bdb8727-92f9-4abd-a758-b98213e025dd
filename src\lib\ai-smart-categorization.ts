import { Transaction } from './types';

export interface CategoryRule {
  id: string;
  name: string;
  category: string;
  subcategory?: string;
  conditions: CategoryCondition[];
  confidence: number;
  priority: number;
  isActive: boolean;
  createdBy: 'system' | 'user' | 'ai';
  lastUsed?: Date;
  usageCount: number;
}

export interface CategoryCondition {
  field: 'description' | 'merchant' | 'amount' | 'reference' | 'account';
  operator: 'contains' | 'equals' | 'starts_with' | 'ends_with' | 'regex' | 'greater_than' | 'less_than' | 'between';
  value: string | number | [number, number];
  caseSensitive?: boolean;
}

export interface CategorySuggestion {
  transactionId: string;
  suggestedCategory: string;
  suggestedSubcategory?: string;
  confidence: number;
  reasoning: string;
  alternativeCategories: Array<{
    category: string;
    confidence: number;
  }>;
}

export interface LearningPattern {
  pattern: string;
  category: string;
  frequency: number;
  accuracy: number;
  lastSeen: Date;
}

export class AISmartCategorization {
  private static readonly SOUTH_AFRICAN_MERCHANTS = {
    // Grocery stores
    'checkers': 'groceries',
    'pick n pay': 'groceries',
    'woolworths': 'groceries',
    'spar': 'groceries',
    'shoprite': 'groceries',
    'food lover': 'groceries',
    
    // Fuel stations
    'shell': 'transport',
    'bp': 'transport',
    'engen': 'transport',
    'sasol': 'transport',
    'caltex': 'transport',
    'total': 'transport',
    
    // Banks and financial
    'fnb': 'fees',
    'standard bank': 'fees',
    'absa': 'fees',
    'nedbank': 'fees',
    'capitec': 'fees',
    
    // Utilities
    'eskom': 'utilities',
    'city of cape town': 'utilities',
    'city of johannesburg': 'utilities',
    'ethekwini': 'utilities',
    'dstv': 'entertainment',
    'multichoice': 'entertainment',
    'openserve': 'utilities',
    'telkom': 'utilities',
    'vodacom': 'utilities',
    'mtn': 'utilities',
    'cell c': 'utilities',
    
    // Retail
    'game': 'shopping',
    'makro': 'shopping',
    'builders warehouse': 'shopping',
    'takealot': 'shopping',
    'mr price': 'shopping',
    'edgars': 'shopping',
    'truworths': 'shopping',
    
    // Restaurants
    'kfc': 'dining',
    'mcdonald': 'dining',
    'nando': 'dining',
    'steers': 'dining',
    'wimpy': 'dining',
    'spur': 'dining',
    
    // Healthcare
    'dis-chem': 'healthcare',
    'clicks': 'healthcare',
    'netcare': 'healthcare',
    'mediclinic': 'healthcare',
    
    // Education
    'university': 'education',
    'college': 'education',
    'school': 'education',
    'tuition': 'education',
  };

  private static readonly CATEGORY_KEYWORDS = {
    groceries: [
      'grocery', 'supermarket', 'food', 'fresh', 'market', 'butchery',
      'bakery', 'deli', 'produce', 'organic', 'health food'
    ],
    transport: [
      'fuel', 'petrol', 'diesel', 'garage', 'service station', 'uber',
      'bolt', 'taxi', 'bus', 'train', 'parking', 'toll', 'car wash',
      'mechanic', 'tyres', 'auto', 'vehicle'
    ],
    utilities: [
      'electricity', 'water', 'gas', 'municipal', 'rates', 'internet',
      'wifi', 'phone', 'mobile', 'data', 'airtime', 'insurance'
    ],
    entertainment: [
      'cinema', 'movie', 'theatre', 'concert', 'music', 'streaming',
      'netflix', 'spotify', 'gaming', 'sport', 'gym', 'fitness'
    ],
    dining: [
      'restaurant', 'cafe', 'coffee', 'takeaway', 'delivery', 'pizza',
      'burger', 'sushi', 'pub', 'bar', 'club', 'catering'
    ],
    shopping: [
      'clothing', 'fashion', 'electronics', 'furniture', 'home',
      'garden', 'hardware', 'pharmacy', 'cosmetics', 'books'
    ],
    healthcare: [
      'doctor', 'dentist', 'hospital', 'clinic', 'medical', 'pharmacy',
      'medicine', 'prescription', 'health', 'wellness', 'therapy'
    ],
    education: [
      'school', 'university', 'college', 'tuition', 'course', 'training',
      'education', 'books', 'stationery', 'fees'
    ],
    fees: [
      'bank', 'fee', 'charge', 'commission', 'interest', 'penalty',
      'admin', 'service charge', 'monthly fee'
    ],
    income: [
      'salary', 'wage', 'bonus', 'commission', 'dividend', 'interest',
      'refund', 'cashback', 'reward'
    ]
  };

  private static readonly AMOUNT_PATTERNS = {
    utilities: { min: 100, max: 2000 }, // Typical utility bills
    groceries: { min: 50, max: 1500 }, // Typical grocery shopping
    transport: { min: 20, max: 800 }, // Fuel and transport
    dining: { min: 30, max: 500 }, // Restaurant meals
    entertainment: { min: 50, max: 300 }, // Entertainment expenses
  };

  static categorizeTransaction(
    transaction: Transaction,
    existingRules: CategoryRule[] = [],
    learningPatterns: LearningPattern[] = []
  ): CategorySuggestion {
    // Try rule-based categorization first
    const ruleMatch = this.applyRules(transaction, existingRules);
    if (ruleMatch) {
      return {
        transactionId: transaction.id,
        suggestedCategory: ruleMatch.category,
        suggestedSubcategory: ruleMatch.subcategory,
        confidence: ruleMatch.confidence,
        reasoning: `Matched rule: ${ruleMatch.name}`,
        alternativeCategories: []
      };
    }

    // Try merchant-based categorization
    const merchantMatch = this.categorizeBySouthAfricanMerchant(transaction);
    if (merchantMatch.confidence > 0.8) {
      return merchantMatch;
    }

    // Try keyword-based categorization
    const keywordMatch = this.categorizeByKeywords(transaction);
    if (keywordMatch.confidence > 0.7) {
      return keywordMatch;
    }

    // Try amount pattern matching
    const amountMatch = this.categorizeByAmountPattern(transaction);
    if (amountMatch.confidence > 0.6) {
      return amountMatch;
    }

    // Try learning pattern matching
    const learningMatch = this.categorizeByLearningPatterns(transaction, learningPatterns);
    if (learningMatch.confidence > 0.5) {
      return learningMatch;
    }

    // Default fallback
    return {
      transactionId: transaction.id,
      suggestedCategory: 'other',
      confidence: 0.3,
      reasoning: 'No clear pattern found, manual categorization recommended',
      alternativeCategories: this.generateAlternativeCategories(transaction)
    };
  }

  private static applyRules(transaction: Transaction, rules: CategoryRule[]): CategoryRule | null {
    const activeRules = rules
      .filter(rule => rule.isActive)
      .sort((a, b) => b.priority - a.priority);

    for (const rule of activeRules) {
      if (this.evaluateRule(transaction, rule)) {
        return rule;
      }
    }

    return null;
  }

  private static evaluateRule(transaction: Transaction, rule: CategoryRule): boolean {
    return rule.conditions.every(condition => this.evaluateCondition(transaction, condition));
  }

  private static evaluateCondition(transaction: Transaction, condition: CategoryCondition): boolean {
    let fieldValue: string | number;

    switch (condition.field) {
      case 'description':
        fieldValue = transaction.description;
        break;
      case 'merchant':
        fieldValue = transaction.merchant || '';
        break;
      case 'amount':
        fieldValue = Math.abs(transaction.amount);
        break;
      case 'reference':
        fieldValue = transaction.reference || '';
        break;
      case 'account':
        fieldValue = transaction.accountId;
        break;
      default:
        return false;
    }

    if (typeof fieldValue === 'string') {
      const value = condition.caseSensitive ? fieldValue : fieldValue.toLowerCase();
      const conditionValue = condition.caseSensitive ? 
        condition.value as string : 
        (condition.value as string).toLowerCase();

      switch (condition.operator) {
        case 'contains':
          return value.includes(conditionValue);
        case 'equals':
          return value === conditionValue;
        case 'starts_with':
          return value.startsWith(conditionValue);
        case 'ends_with':
          return value.endsWith(conditionValue);
        case 'regex':
          try {
            const regex = new RegExp(conditionValue);
            return regex.test(value);
          } catch {
            return false;
          }
        default:
          return false;
      }
    } else if (typeof fieldValue === 'number') {
      const numValue = condition.value as number;
      const rangeValue = condition.value as [number, number];

      switch (condition.operator) {
        case 'equals':
          return fieldValue === numValue;
        case 'greater_than':
          return fieldValue > numValue;
        case 'less_than':
          return fieldValue < numValue;
        case 'between':
          return fieldValue >= rangeValue[0] && fieldValue <= rangeValue[1];
        default:
          return false;
      }
    }

    return false;
  }

  private static categorizeBySouthAfricanMerchant(transaction: Transaction): CategorySuggestion {
    if (!transaction.merchant) {
      return this.createLowConfidenceSuggestion(transaction);
    }

    const merchant = transaction.merchant.toLowerCase();
    
    for (const [merchantName, category] of Object.entries(this.SOUTH_AFRICAN_MERCHANTS)) {
      if (merchant.includes(merchantName)) {
        return {
          transactionId: transaction.id,
          suggestedCategory: category,
          confidence: 0.9,
          reasoning: `Recognized South African merchant: ${merchantName}`,
          alternativeCategories: []
        };
      }
    }

    return this.createLowConfidenceSuggestion(transaction);
  }

  private static categorizeByKeywords(transaction: Transaction): CategorySuggestion {
    const text = `${transaction.description} ${transaction.merchant || ''}`.toLowerCase();
    const scores: Record<string, number> = {};

    Object.entries(this.CATEGORY_KEYWORDS).forEach(([category, keywords]) => {
      const matches = keywords.filter(keyword => text.includes(keyword));
      scores[category] = matches.length / keywords.length;
    });

    const bestMatch = Object.entries(scores)
      .filter(([, score]) => score > 0)
      .sort(([, a], [, b]) => b - a)[0];

    if (bestMatch && bestMatch[1] > 0.2) {
      return {
        transactionId: transaction.id,
        suggestedCategory: bestMatch[0],
        confidence: Math.min(0.8, bestMatch[1] * 2),
        reasoning: `Keyword match in description/merchant`,
        alternativeCategories: Object.entries(scores)
          .filter(([category, score]) => category !== bestMatch[0] && score > 0.1)
          .map(([category, score]) => ({ category, confidence: score }))
          .slice(0, 3)
      };
    }

    return this.createLowConfidenceSuggestion(transaction);
  }

  private static categorizeByAmountPattern(transaction: Transaction): CategorySuggestion {
    const amount = Math.abs(transaction.amount);
    const matches: Array<{ category: string; confidence: number }> = [];

    Object.entries(this.AMOUNT_PATTERNS).forEach(([category, pattern]) => {
      if (amount >= pattern.min && amount <= pattern.max) {
        const midpoint = (pattern.min + pattern.max) / 2;
        const distance = Math.abs(amount - midpoint);
        const maxDistance = (pattern.max - pattern.min) / 2;
        const confidence = 1 - (distance / maxDistance);
        
        matches.push({ category, confidence: confidence * 0.6 }); // Max 0.6 for amount-only matching
      }
    });

    const bestMatch = matches.sort((a, b) => b.confidence - a.confidence)[0];

    if (bestMatch && bestMatch.confidence > 0.3) {
      return {
        transactionId: transaction.id,
        suggestedCategory: bestMatch.category,
        confidence: bestMatch.confidence,
        reasoning: `Amount pattern suggests ${bestMatch.category}`,
        alternativeCategories: matches.slice(1, 4)
      };
    }

    return this.createLowConfidenceSuggestion(transaction);
  }

  private static categorizeByLearningPatterns(
    transaction: Transaction, 
    patterns: LearningPattern[]
  ): CategorySuggestion {
    const text = `${transaction.description} ${transaction.merchant || ''}`.toLowerCase();
    
    const matches = patterns
      .filter(pattern => text.includes(pattern.pattern.toLowerCase()))
      .sort((a, b) => (b.frequency * b.accuracy) - (a.frequency * a.accuracy));

    const bestMatch = matches[0];
    if (bestMatch && bestMatch.accuracy > 0.7) {
      return {
        transactionId: transaction.id,
        suggestedCategory: bestMatch.category,
        confidence: bestMatch.accuracy * 0.8,
        reasoning: `Learned from similar transactions`,
        alternativeCategories: matches.slice(1, 4).map(m => ({
          category: m.category,
          confidence: m.accuracy * 0.7
        }))
      };
    }

    return this.createLowConfidenceSuggestion(transaction);
  }

  private static createLowConfidenceSuggestion(transaction: Transaction): CategorySuggestion {
    return {
      transactionId: transaction.id,
      suggestedCategory: 'other',
      confidence: 0.2,
      reasoning: 'Unable to determine category automatically',
      alternativeCategories: []
    };
  }

  private static generateAlternativeCategories(transaction: Transaction): Array<{ category: string; confidence: number }> {
    // Generate some reasonable alternatives based on common categories
    const commonCategories = ['groceries', 'transport', 'utilities', 'entertainment', 'dining'];
    return commonCategories.map(category => ({
      category,
      confidence: 0.2
    }));
  }

  // Learning and improvement methods
  static learnFromUserCorrection(
    transaction: Transaction,
    suggestedCategory: string,
    actualCategory: string,
    patterns: LearningPattern[]
  ): LearningPattern[] {
    const text = transaction.description.toLowerCase();
    const words = text.split(/\s+/).filter(word => word.length > 2);

    const updatedPatterns = [...patterns];

    // Update or create patterns for significant words
    words.forEach(word => {
      const existingPattern = updatedPatterns.find(p => p.pattern === word);
      
      if (existingPattern) {
        if (existingPattern.category === actualCategory) {
          existingPattern.frequency++;
          existingPattern.accuracy = Math.min(1, existingPattern.accuracy + 0.1);
        } else {
          existingPattern.accuracy = Math.max(0, existingPattern.accuracy - 0.1);
        }
        existingPattern.lastSeen = new Date();
      } else {
        updatedPatterns.push({
          pattern: word,
          category: actualCategory,
          frequency: 1,
          accuracy: 0.7,
          lastSeen: new Date()
        });
      }
    });

    // Clean up old or low-accuracy patterns
    return updatedPatterns.filter(p => 
      p.accuracy > 0.3 && 
      (Date.now() - p.lastSeen.getTime()) < (90 * 24 * 60 * 60 * 1000) // 90 days
    );
  }

  static createRuleFromPattern(
    pattern: string,
    category: string,
    subcategory?: string
  ): CategoryRule {
    return {
      id: `rule_${Date.now()}`,
      name: `Auto-generated rule for ${pattern}`,
      category,
      subcategory,
      conditions: [
        {
          field: 'description',
          operator: 'contains',
          value: pattern,
          caseSensitive: false
        }
      ],
      confidence: 0.8,
      priority: 5,
      isActive: true,
      createdBy: 'ai',
      usageCount: 0
    };
  }

  static suggestNewRules(transactions: Transaction[]): CategoryRule[] {
    const suggestions: CategoryRule[] = [];
    const merchantCounts: Record<string, { category: string; count: number }> = {};

    // Analyze merchant patterns
    transactions.forEach(t => {
      if (t.merchant && t.category !== 'other') {
        const key = t.merchant.toLowerCase();
        if (merchantCounts[key]) {
          if (merchantCounts[key].category === t.category) {
            merchantCounts[key].count++;
          }
        } else {
          merchantCounts[key] = { category: t.category, count: 1 };
        }
      }
    });

    // Create rules for merchants with consistent categorization
    Object.entries(merchantCounts).forEach(([merchant, data]) => {
      if (data.count >= 3) { // At least 3 transactions
        suggestions.push({
          id: `merchant_rule_${merchant}`,
          name: `Auto-categorize ${merchant}`,
          category: data.category,
          conditions: [
            {
              field: 'merchant',
              operator: 'contains',
              value: merchant,
              caseSensitive: false
            }
          ],
          confidence: Math.min(0.9, 0.5 + (data.count * 0.1)),
          priority: 7,
          isActive: false, // Require user approval
          createdBy: 'ai',
          usageCount: 0
        });
      }
    });

    return suggestions;
  }
}
