"use client";

import { useState } from 'react';
import { formatCurrency, formatDate } from "@/lib/utils";
import { Transaction } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { MobileCard } from "@/components/layout/mobile-nav";
import { 
  ShoppingCart, 
  Car, 
  Gamepad2, 
  Zap, 
  Heart, 
  GraduationCap,
  ShoppingBag,
  UtensilsCrossed,
  Plane,
  Shield,
  TrendingUp,
  Banknote,
  ArrowRightLeft,
  CreditCard,
  MoreHorizontal,
  ChevronRight,
  MapPin,
  Clock,
  Tag,
  Edit,
  Share,
  Receipt
} from "lucide-react";

const categoryIcons: Record<string, any> = {
  groceries: ShoppingCart,
  transport: Car,
  entertainment: Gamepad2,
  utilities: Zap,
  healthcare: Heart,
  education: GraduationCap,
  shopping: ShoppingBag,
  dining: UtensilsCrossed,
  travel: Plane,
  insurance: Shield,
  investments: TrendingUp,
  income: Banknote,
  transfers: ArrowRightLeft,
  fees: CreditCard,
  other: MoreHorizontal,
};

const categoryColors: Record<string, string> = {
  groceries: "bg-green-100 text-green-600",
  transport: "bg-blue-100 text-blue-600",
  entertainment: "bg-purple-100 text-purple-600",
  utilities: "bg-yellow-100 text-yellow-600",
  healthcare: "bg-red-100 text-red-600",
  education: "bg-indigo-100 text-indigo-600",
  shopping: "bg-pink-100 text-pink-600",
  dining: "bg-orange-100 text-orange-600",
  travel: "bg-cyan-100 text-cyan-600",
  insurance: "bg-gray-100 text-gray-600",
  investments: "bg-emerald-100 text-emerald-600",
  income: "bg-green-100 text-green-600",
  transfers: "bg-blue-100 text-blue-600",
  fees: "bg-red-100 text-red-600",
  other: "bg-gray-100 text-gray-600",
};

interface MobileTransactionCardProps {
  transaction: Transaction;
  showAccount?: boolean;
  onEdit?: (transaction: Transaction) => void;
  onShare?: (transaction: Transaction) => void;
  onViewReceipt?: (transaction: Transaction) => void;
}

export function MobileTransactionCard({ 
  transaction, 
  showAccount = false,
  onEdit,
  onShare,
  onViewReceipt
}: MobileTransactionCardProps) {
  const [showDetails, setShowDetails] = useState(false);
  const IconComponent = categoryIcons[transaction.category] || MoreHorizontal;
  const colorClass = categoryColors[transaction.category] || "bg-gray-100 text-gray-600";

  const handleCardTap = () => {
    setShowDetails(!showDetails);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-ZA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  return (
    <MobileCard 
      className="active:bg-gray-50 cursor-pointer transition-colors"
      onClick={handleCardTap}
    >
      {/* Main Transaction Info */}
      <div className="flex items-center space-x-3">
        {/* Category Icon */}
        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${colorClass}`}>
          <IconComponent className="w-6 h-6" />
        </div>

        {/* Transaction Details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 truncate">
                {transaction.description}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="secondary" className="text-xs">
                  {transaction.category.replace('_', ' ')}
                </Badge>
                {transaction.merchant && (
                  <span className="text-xs text-gray-500 truncate">
                    {transaction.merchant}
                  </span>
                )}
              </div>
            </div>
            
            {/* Amount */}
            <div className="text-right ml-3">
              <div className={`font-semibold text-lg ${
                transaction.type === 'credit' ? 'text-green-600' : 'text-gray-900'
              }`}>
                {transaction.type === 'credit' ? '+' : '-'}
                {formatCurrency(Math.abs(transaction.amount), transaction.currency)}
              </div>
              <div className="text-xs text-gray-500">
                {formatTime(new Date(transaction.date))}
              </div>
            </div>
          </div>
        </div>

        {/* Expand Indicator */}
        <ChevronRight 
          className={`w-5 h-5 text-gray-400 transition-transform ${
            showDetails ? 'rotate-90' : ''
          }`} 
        />
      </div>

      {/* Expanded Details */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-100 space-y-3">
          {/* Date and Time */}
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Clock className="w-4 h-4" />
            <span>{formatDate(new Date(transaction.date))}</span>
          </div>

          {/* Location */}
          {transaction.location && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MapPin className="w-4 h-4" />
              <span>{transaction.location}</span>
            </div>
          )}

          {/* Tags */}
          {transaction.tags && transaction.tags.length > 0 && (
            <div className="flex items-center space-x-2">
              <Tag className="w-4 h-4 text-gray-400" />
              <div className="flex flex-wrap gap-1">
                {transaction.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Subcategory */}
          {transaction.subcategory && (
            <div className="text-sm text-gray-600">
              <span className="font-medium">Subcategory:</span> {transaction.subcategory}
            </div>
          )}

          {/* Recurring Indicator */}
          {transaction.isRecurring && (
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                Recurring
              </Badge>
            </div>
          )}

          {/* Account Info */}
          {showAccount && (
            <div className="text-sm text-gray-600">
              <span className="font-medium">Account:</span> {/* Account name would go here */}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2 pt-2">
            {onEdit && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(transaction);
                }}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-100 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-200 transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>Edit</span>
              </button>
            )}
            
            {onShare && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onShare(transaction);
                }}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-100 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-200 transition-colors"
              >
                <Share className="w-4 h-4" />
                <span>Share</span>
              </button>
            )}
            
            {onViewReceipt && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onViewReceipt(transaction);
                }}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-100 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-200 transition-colors"
              >
                <Receipt className="w-4 h-4" />
                <span>Receipt</span>
              </button>
            )}
          </div>
        </div>
      )}
    </MobileCard>
  );
}

// Mobile-optimized transaction list
interface MobileTransactionListProps {
  transactions: Transaction[];
  showAccount?: boolean;
  onEdit?: (transaction: Transaction) => void;
  onShare?: (transaction: Transaction) => void;
  onViewReceipt?: (transaction: Transaction) => void;
}

export function MobileTransactionList({ 
  transactions, 
  showAccount = false,
  onEdit,
  onShare,
  onViewReceipt
}: MobileTransactionListProps) {
  if (transactions.length === 0) {
    return (
      <MobileCard className="text-center py-8">
        <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
        <p className="text-gray-600">Your transactions will appear here</p>
      </MobileCard>
    );
  }

  // Group transactions by date
  const groupedTransactions = transactions.reduce((groups, transaction) => {
    const date = formatDate(new Date(transaction.date));
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(transaction);
    return groups;
  }, {} as Record<string, Transaction[]>);

  return (
    <div className="space-y-4">
      {Object.entries(groupedTransactions).map(([date, dayTransactions]) => (
        <div key={date}>
          {/* Date Header */}
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900">{date}</h3>
            <div className="text-sm text-gray-500">
              {dayTransactions.length} transaction{dayTransactions.length > 1 ? 's' : ''}
            </div>
          </div>

          {/* Transactions for this date */}
          <div className="space-y-3">
            {dayTransactions.map((transaction) => (
              <MobileTransactionCard
                key={transaction.id}
                transaction={transaction}
                showAccount={showAccount}
                onEdit={onEdit}
                onShare={onShare}
                onViewReceipt={onViewReceipt}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
