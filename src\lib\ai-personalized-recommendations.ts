import { Transaction } from './types';
import { FinancialProfile } from './ai-financial-coach';

export interface PersonalizedRecommendation {
  id: string;
  type: 'savings' | 'spending' | 'investment' | 'debt' | 'goal' | 'tax' | 'insurance';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  impact: {
    financial: number; // Estimated financial impact in ZAR
    timeframe: string;
    confidence: number;
  };
  actionSteps: string[];
  requirements: string[];
  southAfricanContext: {
    regulations?: string[];
    localProviders?: string[];
    taxImplications?: string;
  };
  personalizedFor: {
    ageGroup: string;
    incomeLevel: string;
    riskProfile: string;
    lifeStage: string;
  };
  expiresAt?: Date;
  isActionable: boolean;
}

export interface RecommendationContext {
  profile: FinancialProfile;
  transactions: Transaction[];
  currentDate: Date;
  marketConditions: {
    interestRates: Record<string, number>;
    inflationRate: number;
    economicOutlook: 'positive' | 'neutral' | 'negative';
  };
  userPreferences: {
    riskTolerance: 'conservative' | 'moderate' | 'aggressive';
    investmentHorizon: 'short' | 'medium' | 'long';
    priorities: string[];
  };
}

export class AIPersonalizedRecommendations {
  private static readonly SOUTH_AFRICAN_FINANCIAL_PRODUCTS = {
    savings: [
      { name: 'Tax-Free Savings Account', provider: 'Various banks', maxContribution: 36000 },
      { name: 'Fixed Deposit', provider: 'Banks', minAmount: 1000 },
      { name: 'Money Market Account', provider: 'Banks', minAmount: 5000 },
      { name: 'Unit Trusts', provider: 'Asset managers', minAmount: 500 },
    ],
    investment: [
      { name: 'JSE ETFs', provider: 'Various', minAmount: 500 },
      { name: 'Retirement Annuity', provider: 'Insurance companies', minAmount: 500 },
      { name: 'Endowment Policy', provider: 'Insurance companies', minAmount: 1000 },
      { name: 'Property Investment', provider: 'REITs', minAmount: 1000 },
    ],
    insurance: [
      { name: 'Life Insurance', provider: 'Insurance companies', coverage: 'Income replacement' },
      { name: 'Disability Insurance', provider: 'Insurance companies', coverage: 'Income protection' },
      { name: 'Medical Aid', provider: 'Medical schemes', coverage: 'Healthcare' },
      { name: 'Short-term Insurance', provider: 'Insurance companies', coverage: 'Assets' },
    ],
    debt: [
      { name: 'Debt Consolidation', provider: 'Banks', purpose: 'Simplify payments' },
      { name: 'Home Loan', provider: 'Banks', purpose: 'Property purchase' },
      { name: 'Vehicle Finance', provider: 'Banks', purpose: 'Vehicle purchase' },
    ]
  };

  private static readonly LIFE_STAGE_PRIORITIES = {
    'young_professional': ['emergency_fund', 'career_development', 'first_home'],
    'family_building': ['life_insurance', 'education_savings', 'family_home'],
    'peak_earning': ['retirement_planning', 'investment_growth', 'tax_optimization'],
    'pre_retirement': ['retirement_preparation', 'debt_reduction', 'healthcare_planning'],
    'retirement': ['income_preservation', 'healthcare_coverage', 'estate_planning']
  };

  static generatePersonalizedRecommendations(context: RecommendationContext): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    
    // Determine life stage
    const lifeStage = this.determineLifeStage(context.profile);
    
    // Generate recommendations by category
    recommendations.push(...this.generateSavingsRecommendations(context, lifeStage));
    recommendations.push(...this.generateInvestmentRecommendations(context, lifeStage));
    recommendations.push(...this.generateDebtRecommendations(context, lifeStage));
    recommendations.push(...this.generateTaxRecommendations(context, lifeStage));
    recommendations.push(...this.generateInsuranceRecommendations(context, lifeStage));
    recommendations.push(...this.generateGoalRecommendations(context, lifeStage));

    // Sort by priority and impact
    return recommendations
      .sort((a, b) => {
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return b.impact.financial - a.impact.financial;
      })
      .slice(0, 10); // Return top 10 recommendations
  }

  private static determineLifeStage(profile: FinancialProfile): string {
    if (profile.age < 30) return 'young_professional';
    if (profile.age < 40 && profile.dependents > 0) return 'family_building';
    if (profile.age < 55) return 'peak_earning';
    if (profile.age < 65) return 'pre_retirement';
    return 'retirement';
  }

  private static generateSavingsRecommendations(
    context: RecommendationContext, 
    lifeStage: string
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    const { profile } = context;
    
    // Emergency fund recommendation
    const emergencyFundTarget = profile.monthlyExpenses * 6;
    const emergencyFundGap = emergencyFundTarget - profile.currentSavings;
    
    if (emergencyFundGap > 0) {
      recommendations.push({
        id: 'emergency_fund',
        type: 'savings',
        priority: profile.currentSavings < profile.monthlyExpenses ? 'urgent' : 'high',
        title: 'Build Emergency Fund',
        description: `You need R${emergencyFundGap.toFixed(2)} more to reach your 6-month emergency fund target of R${emergencyFundTarget.toFixed(2)}.`,
        impact: {
          financial: emergencyFundGap,
          timeframe: `${Math.ceil(emergencyFundGap / (profile.monthlyIncome * 0.1))} months`,
          confidence: 0.95
        },
        actionSteps: [
          'Open a high-yield savings account',
          'Set up automatic transfer of 10% of income',
          'Keep emergency fund separate from daily banking',
          'Review and adjust monthly'
        ],
        requirements: [
          'Stable income',
          'Basic banking account',
          'Discipline to not touch emergency funds'
        ],
        southAfricanContext: {
          localProviders: ['FNB Money Maximizer', 'Capitec Global One', 'Nedbank Money Maximizer'],
          regulations: ['No tax implications for savings account interest under R23,800 annually']
        },
        personalizedFor: {
          ageGroup: this.getAgeGroup(profile.age),
          incomeLevel: this.getIncomeLevel(profile.monthlyIncome),
          riskProfile: 'conservative',
          lifeStage
        },
        isActionable: true
      });
    }

    // Tax-Free Savings Account
    if (profile.monthlyIncome > 15000) {
      recommendations.push({
        id: 'tfsa_recommendation',
        type: 'savings',
        priority: 'medium',
        title: 'Maximize Tax-Free Savings Account',
        description: 'Use your annual R36,000 TFSA allowance for tax-free investment growth.',
        impact: {
          financial: 36000 * 0.08 * 10, // Estimated 8% return over 10 years
          timeframe: '10 years',
          confidence: 0.8
        },
        actionSteps: [
          'Research TFSA providers and investment options',
          'Set up monthly R3,000 contribution',
          'Choose appropriate asset allocation',
          'Monitor performance quarterly'
        ],
        requirements: [
          'South African tax resident',
          'Available R3,000 monthly',
          'Long-term investment horizon'
        ],
        southAfricanContext: {
          localProviders: ['Allan Gray', 'Coronation', 'Investec', 'Sygnia'],
          regulations: ['R36,000 annual contribution limit', 'R500,000 lifetime limit'],
          taxImplications: 'No tax on growth or withdrawals'
        },
        personalizedFor: {
          ageGroup: this.getAgeGroup(profile.age),
          incomeLevel: this.getIncomeLevel(profile.monthlyIncome),
          riskProfile: context.userPreferences.riskTolerance,
          lifeStage
        },
        isActionable: true
      });
    }

    return recommendations;
  }

  private static generateInvestmentRecommendations(
    context: RecommendationContext, 
    lifeStage: string
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    const { profile, userPreferences } = context;

    // Retirement Annuity recommendation
    if (profile.age < 55 && profile.monthlyIncome > 20000) {
      const recommendedContribution = profile.monthlyIncome * 0.15; // 15% of income
      
      recommendations.push({
        id: 'retirement_annuity',
        type: 'investment',
        priority: lifeStage === 'peak_earning' ? 'high' : 'medium',
        title: 'Start Retirement Annuity',
        description: `Contribute R${recommendedContribution.toFixed(2)} monthly (15% of income) to secure your retirement.`,
        impact: {
          financial: recommendedContribution * 12 * (65 - profile.age) * 1.08, // Compound growth estimate
          timeframe: `${65 - profile.age} years until retirement`,
          confidence: 0.85
        },
        actionSteps: [
          'Compare retirement annuity providers',
          'Choose appropriate fund allocation',
          'Set up debit order',
          'Review annually and increase contributions'
        ],
        requirements: [
          'Stable income',
          'Long-term commitment',
          'Understanding of investment risk'
        ],
        southAfricanContext: {
          localProviders: ['Old Mutual', 'Sanlam', 'Discovery', 'Allan Gray'],
          regulations: ['27.5% of income tax deductible', 'Cannot access before age 55'],
          taxImplications: 'Tax deductible contributions, taxed on retirement'
        },
        personalizedFor: {
          ageGroup: this.getAgeGroup(profile.age),
          incomeLevel: this.getIncomeLevel(profile.monthlyIncome),
          riskProfile: userPreferences.riskTolerance,
          lifeStage
        },
        isActionable: true
      });
    }

    // JSE ETF recommendation for younger investors
    if (profile.age < 45 && userPreferences.riskTolerance !== 'conservative') {
      recommendations.push({
        id: 'jse_etf_investment',
        type: 'investment',
        priority: 'medium',
        title: 'Invest in JSE ETFs',
        description: 'Start with low-cost JSE ETFs for diversified South African and global exposure.',
        impact: {
          financial: 5000 * 12 * 0.12 * 10, // R5k monthly at 12% for 10 years
          timeframe: '10+ years',
          confidence: 0.7
        },
        actionSteps: [
          'Open investment account with broker',
          'Research top JSE ETFs (STXIND, ASHGEQ, etc.)',
          'Start with R1,000 monthly investment',
          'Gradually increase contributions'
        ],
        requirements: [
          'Investment account',
          'Risk tolerance for market volatility',
          'Long-term investment horizon'
        ],
        southAfricanContext: {
          localProviders: ['EasyEquities', 'Standard Bank', 'Investec'],
          regulations: ['Subject to capital gains tax', 'Dividend withholding tax applies']
        },
        personalizedFor: {
          ageGroup: this.getAgeGroup(profile.age),
          incomeLevel: this.getIncomeLevel(profile.monthlyIncome),
          riskProfile: userPreferences.riskTolerance,
          lifeStage
        },
        isActionable: true
      });
    }

    return recommendations;
  }

  private static generateDebtRecommendations(
    context: RecommendationContext, 
    lifeStage: string
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    const { profile } = context;

    if (profile.currentDebt > 0) {
      const debtToIncomeRatio = profile.currentDebt / (profile.monthlyIncome * 12);
      
      if (debtToIncomeRatio > 0.3) {
        recommendations.push({
          id: 'debt_consolidation',
          type: 'debt',
          priority: 'high',
          title: 'Consider Debt Consolidation',
          description: `Your debt-to-income ratio is ${(debtToIncomeRatio * 100).toFixed(1)}%. Consolidation could reduce monthly payments.`,
          impact: {
            financial: profile.currentDebt * 0.05, // Estimated 5% savings
            timeframe: '2-5 years',
            confidence: 0.75
          },
          actionSteps: [
            'List all current debts and interest rates',
            'Compare consolidation loan offers',
            'Calculate total cost of each option',
            'Choose best option and apply'
          ],
          requirements: [
            'Good credit score',
            'Stable income',
            'Discipline to not accumulate new debt'
          ],
          southAfricanContext: {
            localProviders: ['Banks', 'Credit providers'],
            regulations: ['National Credit Act protections apply']
          },
          personalizedFor: {
            ageGroup: this.getAgeGroup(profile.age),
            incomeLevel: this.getIncomeLevel(profile.monthlyIncome),
            riskProfile: 'conservative',
            lifeStage
          },
          isActionable: true
        });
      }
    }

    return recommendations;
  }

  private static generateTaxRecommendations(
    context: RecommendationContext, 
    lifeStage: string
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    const { profile } = context;

    if (profile.monthlyIncome * 12 > 237100) { // Above tax threshold
      recommendations.push({
        id: 'tax_optimization',
        type: 'tax',
        priority: 'medium',
        title: 'Optimize Tax Deductions',
        description: 'Maximize your tax deductions through retirement contributions and medical aid.',
        impact: {
          financial: profile.monthlyIncome * 12 * 0.05, // Estimated 5% tax savings
          timeframe: 'Annual',
          confidence: 0.9
        },
        actionSteps: [
          'Maximize retirement annuity contributions',
          'Ensure medical aid compliance',
          'Keep records of tax-deductible expenses',
          'Consider tax-free investments'
        ],
        requirements: [
          'Taxable income',
          'Proper record keeping',
          'Understanding of tax regulations'
        ],
        southAfricanContext: {
          regulations: [
            '27.5% of income deductible for retirement',
            'Medical aid tax credits available',
            'TFSA contributions not tax deductible'
          ]
        },
        personalizedFor: {
          ageGroup: this.getAgeGroup(profile.age),
          incomeLevel: this.getIncomeLevel(profile.monthlyIncome),
          riskProfile: 'conservative',
          lifeStage
        },
        isActionable: true
      });
    }

    return recommendations;
  }

  private static generateInsuranceRecommendations(
    context: RecommendationContext, 
    lifeStage: string
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    const { profile } = context;

    if (profile.dependents > 0 && lifeStage !== 'retirement') {
      const lifeInsuranceNeeded = profile.monthlyIncome * 12 * 10; // 10 years of income
      
      recommendations.push({
        id: 'life_insurance',
        type: 'insurance',
        priority: 'high',
        title: 'Secure Life Insurance',
        description: `With ${profile.dependents} dependent(s), consider R${lifeInsuranceNeeded.toFixed(2)} life insurance coverage.`,
        impact: {
          financial: lifeInsuranceNeeded,
          timeframe: 'Immediate protection',
          confidence: 0.95
        },
        actionSteps: [
          'Calculate insurance needs',
          'Compare term vs whole life policies',
          'Get quotes from multiple providers',
          'Choose appropriate coverage amount'
        ],
        requirements: [
          'Medical examination',
          'Income verification',
          'Monthly premium budget'
        ],
        southAfricanContext: {
          localProviders: ['Old Mutual', 'Sanlam', 'Discovery Life', 'Momentum'],
          regulations: ['Life insurance proceeds generally tax-free']
        },
        personalizedFor: {
          ageGroup: this.getAgeGroup(profile.age),
          incomeLevel: this.getIncomeLevel(profile.monthlyIncome),
          riskProfile: 'conservative',
          lifeStage
        },
        isActionable: true
      });
    }

    return recommendations;
  }

  private static generateGoalRecommendations(
    context: RecommendationContext, 
    lifeStage: string
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    // Implementation for goal-specific recommendations
    return recommendations;
  }

  private static getAgeGroup(age: number): string {
    if (age < 30) return '20s';
    if (age < 40) return '30s';
    if (age < 50) return '40s';
    if (age < 60) return '50s';
    return '60+';
  }

  private static getIncomeLevel(monthlyIncome: number): string {
    if (monthlyIncome < 15000) return 'entry_level';
    if (monthlyIncome < 30000) return 'middle_income';
    if (monthlyIncome < 60000) return 'upper_middle';
    return 'high_income';
  }
}
