import { Transaction } from './types';

export interface FraudAlert {
  id: string;
  transactionId: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number; // 0-100
  alertType: 'velocity' | 'amount' | 'location' | 'merchant' | 'pattern' | 'device' | 'time';
  description: string;
  detectedAt: Date;
  factors: FraudFactor[];
  recommendedAction: 'monitor' | 'verify' | 'block' | 'investigate';
  isResolved: boolean;
  falsePositive?: boolean;
}

export interface FraudFactor {
  type: string;
  description: string;
  weight: number;
  value: string | number;
}

export interface UserBehaviorProfile {
  userId: string;
  typicalSpendingPatterns: {
    averageTransactionAmount: number;
    commonMerchants: string[];
    usualTransactionTimes: number[]; // Hours of day
    frequentCategories: string[];
    monthlySpendingRange: { min: number; max: number };
  };
  locationPatterns: {
    commonLocations: string[];
    homeLocation?: { lat: number; lng: number };
    workLocation?: { lat: number; lng: number };
  };
  deviceFingerprints: {
    trustedDevices: string[];
    lastKnownDevice: string;
  };
  riskFactors: {
    accountAge: number; // days
    verificationLevel: 'basic' | 'enhanced' | 'full';
    previousFraudIncidents: number;
    creditScore?: number;
  };
  lastUpdated: Date;
}

export interface FraudDetectionConfig {
  velocityLimits: {
    transactionsPerHour: number;
    transactionsPerDay: number;
    amountPerHour: number;
    amountPerDay: number;
  };
  amountThresholds: {
    unusualTransactionMultiplier: number; // e.g., 5x average
    highValueThreshold: number;
    microTransactionThreshold: number;
  };
  timeBasedRules: {
    unusualHourStart: number; // 22 (10 PM)
    unusualHourEnd: number; // 6 (6 AM)
    weekendRiskMultiplier: number;
  };
  locationRules: {
    maxDistanceFromHome: number; // km
    foreignCountryRisk: boolean;
    highRiskCountries: string[];
  };
  southAfricanSpecific: {
    bankingHours: { start: number; end: number };
    publicHolidays: string[];
    highRiskAreas: string[];
    commonFraudPatterns: string[];
  };
}

export class FraudDetectionEngine {
  private static readonly DEFAULT_CONFIG: FraudDetectionConfig = {
    velocityLimits: {
      transactionsPerHour: 10,
      transactionsPerDay: 50,
      amountPerHour: 10000,
      amountPerDay: 50000,
    },
    amountThresholds: {
      unusualTransactionMultiplier: 5,
      highValueThreshold: 10000,
      microTransactionThreshold: 5,
    },
    timeBasedRules: {
      unusualHourStart: 22,
      unusualHourEnd: 6,
      weekendRiskMultiplier: 1.2,
    },
    locationRules: {
      maxDistanceFromHome: 100,
      foreignCountryRisk: true,
      highRiskCountries: ['NG', 'GH', 'KE'], // Example high-risk countries for SA
    },
    southAfricanSpecific: {
      bankingHours: { start: 8, end: 16 },
      publicHolidays: [
        '2024-01-01', '2024-03-21', '2024-04-27', '2024-05-01',
        '2024-06-16', '2024-08-09', '2024-09-24', '2024-12-16', '2024-12-25', '2024-12-26'
      ],
      highRiskAreas: ['Hillbrow', 'Sunnyside', 'Yeoville'], // Example high-risk areas
      commonFraudPatterns: [
        'card_not_present',
        'atm_skimming',
        'phishing',
        'sim_swap',
        'social_engineering'
      ]
    }
  };

  static analyzeTransaction(
    transaction: Transaction,
    userProfile: UserBehaviorProfile,
    recentTransactions: Transaction[],
    config: FraudDetectionConfig = this.DEFAULT_CONFIG
  ): FraudAlert | null {
    const factors: FraudFactor[] = [];
    let riskScore = 0;

    // Velocity checks
    const velocityFactors = this.checkVelocity(transaction, recentTransactions, config);
    factors.push(...velocityFactors);
    riskScore += velocityFactors.reduce((sum, f) => sum + f.weight, 0);

    // Amount analysis
    const amountFactors = this.checkAmountAnomalies(transaction, userProfile, config);
    factors.push(...amountFactors);
    riskScore += amountFactors.reduce((sum, f) => sum + f.weight, 0);

    // Time-based analysis
    const timeFactors = this.checkTimeAnomalies(transaction, userProfile, config);
    factors.push(...timeFactors);
    riskScore += timeFactors.reduce((sum, f) => sum + f.weight, 0);

    // Location analysis
    const locationFactors = this.checkLocationAnomalies(transaction, userProfile, config);
    factors.push(...locationFactors);
    riskScore += locationFactors.reduce((sum, f) => sum + f.weight, 0);

    // Merchant analysis
    const merchantFactors = this.checkMerchantRisk(transaction, userProfile);
    factors.push(...merchantFactors);
    riskScore += merchantFactors.reduce((sum, f) => sum + f.weight, 0);

    // Pattern analysis
    const patternFactors = this.checkPatternAnomalies(transaction, recentTransactions, userProfile);
    factors.push(...patternFactors);
    riskScore += patternFactors.reduce((sum, f) => sum + f.weight, 0);

    // South African specific checks
    const saFactors = this.checkSouthAfricanSpecificRisks(transaction, config);
    factors.push(...saFactors);
    riskScore += saFactors.reduce((sum, f) => sum + f.weight, 0);

    // Determine if alert should be created
    if (riskScore >= 30 || factors.some(f => f.weight >= 25)) {
      return {
        id: `fraud_alert_${Date.now()}`,
        transactionId: transaction.id,
        riskLevel: this.calculateRiskLevel(riskScore),
        riskScore,
        alertType: this.determineAlertType(factors),
        description: this.generateAlertDescription(factors, riskScore),
        detectedAt: new Date(),
        factors,
        recommendedAction: this.determineRecommendedAction(riskScore, factors),
        isResolved: false,
      };
    }

    return null;
  }

  private static checkVelocity(
    transaction: Transaction,
    recentTransactions: Transaction[],
    config: FraudDetectionConfig
  ): FraudFactor[] {
    const factors: FraudFactor[] = [];
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;

    // Check transactions in last hour
    const hourlyTransactions = recentTransactions.filter(
      t => now - t.date <= oneHour
    );
    
    if (hourlyTransactions.length >= config.velocityLimits.transactionsPerHour) {
      factors.push({
        type: 'velocity',
        description: `${hourlyTransactions.length} transactions in the last hour`,
        weight: 20,
        value: hourlyTransactions.length,
      });
    }

    // Check amount velocity
    const hourlyAmount = hourlyTransactions.reduce((sum, t) => sum + Math.abs(t.amount), 0);
    if (hourlyAmount >= config.velocityLimits.amountPerHour) {
      factors.push({
        type: 'velocity',
        description: `R${hourlyAmount} spent in the last hour`,
        weight: 25,
        value: hourlyAmount,
      });
    }

    // Check daily limits
    const dailyTransactions = recentTransactions.filter(
      t => now - t.date <= oneDay
    );
    
    if (dailyTransactions.length >= config.velocityLimits.transactionsPerDay) {
      factors.push({
        type: 'velocity',
        description: `${dailyTransactions.length} transactions today`,
        weight: 15,
        value: dailyTransactions.length,
      });
    }

    return factors;
  }

  private static checkAmountAnomalies(
    transaction: Transaction,
    userProfile: UserBehaviorProfile,
    config: FraudDetectionConfig
  ): FraudFactor[] {
    const factors: FraudFactor[] = [];
    const amount = Math.abs(transaction.amount);
    const avgAmount = userProfile.typicalSpendingPatterns.averageTransactionAmount;

    // Check for unusually large transactions
    if (amount > avgAmount * config.amountThresholds.unusualTransactionMultiplier) {
      factors.push({
        type: 'amount',
        description: `Transaction amount R${amount} is ${(amount / avgAmount).toFixed(1)}x larger than usual`,
        weight: 20,
        value: amount,
      });
    }

    // Check for high-value transactions
    if (amount >= config.amountThresholds.highValueThreshold) {
      factors.push({
        type: 'amount',
        description: `High-value transaction: R${amount}`,
        weight: 15,
        value: amount,
      });
    }

    // Check for micro-transactions (potential testing)
    if (amount <= config.amountThresholds.microTransactionThreshold) {
      factors.push({
        type: 'amount',
        description: `Micro-transaction: R${amount} (potential card testing)`,
        weight: 10,
        value: amount,
      });
    }

    return factors;
  }

  private static checkTimeAnomalies(
    transaction: Transaction,
    userProfile: UserBehaviorProfile,
    config: FraudDetectionConfig
  ): FraudFactor[] {
    const factors: FraudFactor[] = [];
    const transactionTime = new Date(transaction.date);
    const hour = transactionTime.getHours();
    const dayOfWeek = transactionTime.getDay();

    // Check for unusual hours
    if (hour >= config.timeBasedRules.unusualHourStart || hour <= config.timeBasedRules.unusualHourEnd) {
      factors.push({
        type: 'time',
        description: `Transaction at unusual hour: ${hour}:00`,
        weight: 12,
        value: hour,
      });
    }

    // Check against user's typical transaction times
    const typicalHours = userProfile.typicalSpendingPatterns.usualTransactionTimes;
    if (typicalHours.length > 0 && !typicalHours.includes(hour)) {
      factors.push({
        type: 'time',
        description: `Transaction outside typical hours`,
        weight: 8,
        value: hour,
      });
    }

    // Weekend transactions (if unusual for user)
    if ((dayOfWeek === 0 || dayOfWeek === 6) && config.timeBasedRules.weekendRiskMultiplier > 1) {
      factors.push({
        type: 'time',
        description: `Weekend transaction`,
        weight: 5,
        value: dayOfWeek,
      });
    }

    return factors;
  }

  private static checkLocationAnomalies(
    transaction: Transaction,
    userProfile: UserBehaviorProfile,
    config: FraudDetectionConfig
  ): FraudFactor[] {
    const factors: FraudFactor[] = [];

    // Check if location is provided
    if (!transaction.location) {
      return factors;
    }

    // Check against common locations
    const commonLocations = userProfile.locationPatterns.commonLocations;
    if (commonLocations.length > 0 && !commonLocations.some(loc => 
      transaction.location?.toLowerCase().includes(loc.toLowerCase())
    )) {
      factors.push({
        type: 'location',
        description: `Transaction in unusual location: ${transaction.location}`,
        weight: 15,
        value: transaction.location,
      });
    }

    // Check for high-risk areas (South African specific)
    const highRiskAreas = config.southAfricanSpecific.highRiskAreas;
    if (highRiskAreas.some(area => 
      transaction.location?.toLowerCase().includes(area.toLowerCase())
    )) {
      factors.push({
        type: 'location',
        description: `Transaction in high-risk area: ${transaction.location}`,
        weight: 25,
        value: transaction.location,
      });
    }

    return factors;
  }

  private static checkMerchantRisk(
    transaction: Transaction,
    userProfile: UserBehaviorProfile
  ): FraudFactor[] {
    const factors: FraudFactor[] = [];

    if (!transaction.merchant) {
      return factors;
    }

    // Check against common merchants
    const commonMerchants = userProfile.typicalSpendingPatterns.commonMerchants;
    if (commonMerchants.length > 0 && !commonMerchants.includes(transaction.merchant)) {
      factors.push({
        type: 'merchant',
        description: `Transaction with new merchant: ${transaction.merchant}`,
        weight: 8,
        value: transaction.merchant,
      });
    }

    // Check for suspicious merchant patterns
    const suspiciousPatterns = [
      'unknown merchant',
      'cash advance',
      'money transfer',
      'cryptocurrency',
      'gambling',
      'adult entertainment'
    ];

    if (suspiciousPatterns.some(pattern => 
      transaction.merchant.toLowerCase().includes(pattern)
    )) {
      factors.push({
        type: 'merchant',
        description: `Transaction with high-risk merchant type`,
        weight: 18,
        value: transaction.merchant,
      });
    }

    return factors;
  }

  private static checkPatternAnomalies(
    transaction: Transaction,
    recentTransactions: Transaction[],
    userProfile: UserBehaviorProfile
  ): FraudFactor[] {
    const factors: FraudFactor[] = [];

    // Check for duplicate transactions
    const duplicates = recentTransactions.filter(t => 
      t.amount === transaction.amount &&
      t.merchant === transaction.merchant &&
      Math.abs(t.date - transaction.date) < 5 * 60 * 1000 // Within 5 minutes
    );

    if (duplicates.length > 0) {
      factors.push({
        type: 'pattern',
        description: `Potential duplicate transaction`,
        weight: 20,
        value: duplicates.length,
      });
    }

    // Check for round number patterns (potential testing)
    if (transaction.amount % 100 === 0 && transaction.amount <= 1000) {
      factors.push({
        type: 'pattern',
        description: `Round number transaction: R${transaction.amount}`,
        weight: 8,
        value: transaction.amount,
      });
    }

    // Check category consistency
    const typicalCategories = userProfile.typicalSpendingPatterns.frequentCategories;
    if (typicalCategories.length > 0 && !typicalCategories.includes(transaction.category)) {
      factors.push({
        type: 'pattern',
        description: `Transaction in unusual category: ${transaction.category}`,
        weight: 6,
        value: transaction.category,
      });
    }

    return factors;
  }

  private static checkSouthAfricanSpecificRisks(
    transaction: Transaction,
    config: FraudDetectionConfig
  ): FraudFactor[] {
    const factors: FraudFactor[] = [];
    const transactionTime = new Date(transaction.date);
    const hour = transactionTime.getHours();

    // Check banking hours
    const bankingHours = config.southAfricanSpecific.bankingHours;
    if (hour < bankingHours.start || hour > bankingHours.end) {
      factors.push({
        type: 'time',
        description: `Transaction outside banking hours`,
        weight: 10,
        value: hour,
      });
    }

    // Check public holidays
    const dateString = transactionTime.toISOString().split('T')[0];
    if (config.southAfricanSpecific.publicHolidays.includes(dateString)) {
      factors.push({
        type: 'time',
        description: `Transaction on public holiday`,
        weight: 8,
        value: dateString,
      });
    }

    return factors;
  }

  private static calculateRiskLevel(riskScore: number): 'low' | 'medium' | 'high' | 'critical' {
    if (riskScore >= 70) return 'critical';
    if (riskScore >= 50) return 'high';
    if (riskScore >= 30) return 'medium';
    return 'low';
  }

  private static determineAlertType(factors: FraudFactor[]): FraudAlert['alertType'] {
    const typeWeights = factors.reduce((acc, factor) => {
      acc[factor.type] = (acc[factor.type] || 0) + factor.weight;
      return acc;
    }, {} as Record<string, number>);

    const dominantType = Object.entries(typeWeights)
      .sort(([, a], [, b]) => b - a)[0];

    return dominantType[0] as FraudAlert['alertType'];
  }

  private static generateAlertDescription(factors: FraudFactor[], riskScore: number): string {
    const topFactors = factors
      .sort((a, b) => b.weight - a.weight)
      .slice(0, 3);

    const descriptions = topFactors.map(f => f.description);
    
    return `Fraud risk detected (score: ${riskScore}). Key factors: ${descriptions.join(', ')}.`;
  }

  private static determineRecommendedAction(
    riskScore: number, 
    factors: FraudFactor[]
  ): FraudAlert['recommendedAction'] {
    if (riskScore >= 70) return 'block';
    if (riskScore >= 50) return 'investigate';
    if (riskScore >= 35) return 'verify';
    return 'monitor';
  }

  // Update user behavior profile based on legitimate transactions
  static updateUserProfile(
    userId: string,
    transaction: Transaction,
    currentProfile: UserBehaviorProfile
  ): UserBehaviorProfile {
    const updatedProfile = { ...currentProfile };
    
    // Update spending patterns
    const patterns = updatedProfile.typicalSpendingPatterns;
    patterns.averageTransactionAmount = (patterns.averageTransactionAmount + Math.abs(transaction.amount)) / 2;
    
    if (transaction.merchant && !patterns.commonMerchants.includes(transaction.merchant)) {
      patterns.commonMerchants.push(transaction.merchant);
      // Keep only top 20 merchants
      if (patterns.commonMerchants.length > 20) {
        patterns.commonMerchants = patterns.commonMerchants.slice(-20);
      }
    }

    const hour = new Date(transaction.date).getHours();
    if (!patterns.usualTransactionTimes.includes(hour)) {
      patterns.usualTransactionTimes.push(hour);
    }

    if (!patterns.frequentCategories.includes(transaction.category)) {
      patterns.frequentCategories.push(transaction.category);
    }

    // Update location patterns
    if (transaction.location && !updatedProfile.locationPatterns.commonLocations.includes(transaction.location)) {
      updatedProfile.locationPatterns.commonLocations.push(transaction.location);
      // Keep only top 10 locations
      if (updatedProfile.locationPatterns.commonLocations.length > 10) {
        updatedProfile.locationPatterns.commonLocations = updatedProfile.locationPatterns.commonLocations.slice(-10);
      }
    }

    updatedProfile.lastUpdated = new Date();
    
    return updatedProfile;
  }

  // Generate fraud prevention recommendations
  static generatePreventionRecommendations(alerts: FraudAlert[]): string[] {
    const recommendations: string[] = [];
    const alertTypes = alerts.map(a => a.alertType);

    if (alertTypes.includes('velocity')) {
      recommendations.push('Consider setting transaction velocity limits');
    }

    if (alertTypes.includes('amount')) {
      recommendations.push('Enable high-value transaction notifications');
    }

    if (alertTypes.includes('location')) {
      recommendations.push('Set up location-based alerts for unusual areas');
    }

    if (alertTypes.includes('time')) {
      recommendations.push('Consider restricting transactions during unusual hours');
    }

    if (alertTypes.includes('merchant')) {
      recommendations.push('Review and whitelist trusted merchants');
    }

    // South African specific recommendations
    recommendations.push('Enable SMS notifications for all transactions');
    recommendations.push('Use secure banking apps with biometric authentication');
    recommendations.push('Regularly monitor your credit report');
    recommendations.push('Be cautious of SIM swap fraud attempts');

    return recommendations;
  }
}
