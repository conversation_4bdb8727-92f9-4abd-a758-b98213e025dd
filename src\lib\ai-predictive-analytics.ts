import { Transaction } from './types';

export interface PredictionModel {
  type: 'spending' | 'income' | 'savings' | 'cashflow';
  accuracy: number;
  confidence: number;
  lastTrained: Date;
  features: string[];
}

export interface SpendingPrediction {
  category: string;
  predictedAmount: number;
  confidence: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  seasonality: boolean;
  factors: string[];
}

export interface CashflowPrediction {
  date: Date;
  predictedBalance: number;
  confidence: number;
  riskLevel: 'low' | 'medium' | 'high';
  alerts: string[];
}

export interface FinancialForecast {
  timeframe: '1_month' | '3_months' | '6_months' | '1_year';
  spendingPredictions: SpendingPrediction[];
  cashflowPredictions: CashflowPrediction[];
  savingsProjection: {
    projected: number;
    confidence: number;
    factors: string[];
  };
  recommendations: string[];
}

export interface AnomalyDetection {
  transactionId: string;
  anomalyType: 'amount' | 'frequency' | 'category' | 'timing' | 'merchant';
  severity: 'low' | 'medium' | 'high';
  description: string;
  confidence: number;
  suggestedAction: string;
}

export class AIPredictiveAnalytics {
  private static readonly SEASONAL_PATTERNS = {
    december: { groceries: 1.4, entertainment: 1.8, shopping: 2.2 }, // Holiday season
    january: { groceries: 0.8, entertainment: 0.6, shopping: 0.7 }, // Post-holiday
    march: { education: 1.5, shopping: 1.2 }, // Back to school
    june: { utilities: 1.3, transport: 0.9 }, // Winter
    november: { shopping: 1.3, entertainment: 1.2 }, // Black Friday
  };

  private static readonly SOUTH_AFRICAN_EVENTS = {
    // Public holidays and events that affect spending
    easter: { groceries: 1.2, travel: 1.5 },
    heritage_day: { entertainment: 1.3, dining: 1.4 },
    december_holidays: { travel: 2.0, entertainment: 1.6, groceries: 1.3 },
    back_to_school: { education: 2.5, shopping: 1.4 },
    year_end_bonus: { shopping: 1.8, savings: 1.5 },
  };

  static generateFinancialForecast(
    transactions: Transaction[],
    timeframe: '1_month' | '3_months' | '6_months' | '1_year',
    currentBalance: number
  ): FinancialForecast {
    const spendingPredictions = this.predictSpending(transactions, timeframe);
    const cashflowPredictions = this.predictCashflow(transactions, currentBalance, timeframe);
    const savingsProjection = this.predictSavings(transactions, timeframe);
    const recommendations = this.generateForecastRecommendations(spendingPredictions, cashflowPredictions);

    return {
      timeframe,
      spendingPredictions,
      cashflowPredictions,
      savingsProjection,
      recommendations,
    };
  }

  private static predictSpending(transactions: Transaction[], timeframe: string): SpendingPrediction[] {
    const predictions: SpendingPrediction[] = [];
    const categoryData = this.analyzeCategoryTrends(transactions);

    Object.entries(categoryData).forEach(([category, data]) => {
      const trend = this.calculateTrend(data.amounts);
      const seasonalFactor = this.getSeasonalFactor(category);
      const baseAmount = this.calculateMovingAverage(data.amounts, 3);
      
      let predictedAmount = baseAmount;
      
      // Apply trend
      if (trend.direction === 'increasing') {
        predictedAmount *= (1 + trend.rate);
      } else if (trend.direction === 'decreasing') {
        predictedAmount *= (1 - trend.rate);
      }

      // Apply seasonal adjustment
      predictedAmount *= seasonalFactor;

      // Adjust for timeframe
      const timeMultiplier = this.getTimeMultiplier(timeframe);
      predictedAmount *= timeMultiplier;

      predictions.push({
        category,
        predictedAmount,
        confidence: this.calculateConfidence(data.amounts),
        trend: trend.direction,
        seasonality: seasonalFactor !== 1,
        factors: this.identifySpendingFactors(category, data),
      });
    });

    return predictions.sort((a, b) => b.predictedAmount - a.predictedAmount);
  }

  private static predictCashflow(
    transactions: Transaction[],
    currentBalance: number,
    timeframe: string
  ): CashflowPrediction[] {
    const predictions: CashflowPrediction[] = [];
    const monthsToPredict = this.getMonthsFromTimeframe(timeframe);
    
    // Analyze income and expense patterns
    const monthlyIncome = this.calculateAverageMonthlyIncome(transactions);
    const monthlyExpenses = this.calculateAverageMonthlyExpenses(transactions);
    const incomeVariability = this.calculateIncomeVariability(transactions);
    const expenseVariability = this.calculateExpenseVariability(transactions);

    let runningBalance = currentBalance;

    for (let month = 1; month <= monthsToPredict; month++) {
      const predictedIncome = monthlyIncome * this.getIncomeSeasonality(month);
      const predictedExpenses = monthlyExpenses * this.getExpenseSeasonality(month);
      
      runningBalance += predictedIncome - predictedExpenses;

      const confidence = Math.max(0.5, 1 - (month * 0.1)); // Confidence decreases over time
      const riskLevel = this.assessCashflowRisk(runningBalance, monthlyExpenses);
      const alerts = this.generateCashflowAlerts(runningBalance, monthlyExpenses, month);

      predictions.push({
        date: new Date(Date.now() + month * 30 * 24 * 60 * 60 * 1000),
        predictedBalance: runningBalance,
        confidence,
        riskLevel,
        alerts,
      });
    }

    return predictions;
  }

  private static predictSavings(transactions: Transaction[], timeframe: string): {
    projected: number;
    confidence: number;
    factors: string[];
  } {
    const historicalSavings = this.calculateHistoricalSavingsRate(transactions);
    const savingsTrend = this.calculateSavingsTrend(transactions);
    const timeMultiplier = this.getTimeMultiplier(timeframe);
    
    let projectedSavings = historicalSavings.averageMonthly * timeMultiplier;
    
    // Apply trend
    if (savingsTrend.direction === 'increasing') {
      projectedSavings *= (1 + savingsTrend.rate);
    } else if (savingsTrend.direction === 'decreasing') {
      projectedSavings *= (1 - savingsTrend.rate);
    }

    const factors = [
      `Historical savings rate: ${(historicalSavings.rate * 100).toFixed(1)}%`,
      `Trend: ${savingsTrend.direction}`,
      `Consistency score: ${historicalSavings.consistency.toFixed(2)}`,
    ];

    return {
      projected: projectedSavings,
      confidence: historicalSavings.consistency,
      factors,
    };
  }

  static detectAnomalies(transactions: Transaction[]): AnomalyDetection[] {
    const anomalies: AnomalyDetection[] = [];
    
    // Detect amount anomalies
    anomalies.push(...this.detectAmountAnomalies(transactions));
    
    // Detect frequency anomalies
    anomalies.push(...this.detectFrequencyAnomalies(transactions));
    
    // Detect category anomalies
    anomalies.push(...this.detectCategoryAnomalies(transactions));
    
    // Detect timing anomalies
    anomalies.push(...this.detectTimingAnomalies(transactions));

    return anomalies.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  private static detectAmountAnomalies(transactions: Transaction[]): AnomalyDetection[] {
    const anomalies: AnomalyDetection[] = [];
    const categoryStats = this.calculateCategoryStatistics(transactions);

    transactions.forEach(transaction => {
      const stats = categoryStats[transaction.category];
      if (!stats) return;

      const zScore = Math.abs((transaction.amount - stats.mean) / stats.stdDev);
      
      if (zScore > 3) { // 3 standard deviations
        anomalies.push({
          transactionId: transaction.id,
          anomalyType: 'amount',
          severity: zScore > 4 ? 'high' : 'medium',
          description: `Transaction amount R${transaction.amount} is ${zScore.toFixed(1)} standard deviations from normal for ${transaction.category}`,
          confidence: Math.min(0.95, zScore / 5),
          suggestedAction: 'Review transaction details and verify accuracy',
        });
      }
    });

    return anomalies;
  }

  private static detectFrequencyAnomalies(transactions: Transaction[]): AnomalyDetection[] {
    const anomalies: AnomalyDetection[] = [];
    const merchantFrequency = this.calculateMerchantFrequency(transactions);

    Object.entries(merchantFrequency).forEach(([merchant, frequency]) => {
      if (frequency.recent > frequency.average * 2) {
        // Find a transaction from this merchant for the ID
        const transaction = transactions.find(t => t.merchant === merchant);
        if (transaction) {
          anomalies.push({
            transactionId: transaction.id,
            anomalyType: 'frequency',
            severity: frequency.recent > frequency.average * 3 ? 'high' : 'medium',
            description: `Unusual frequency of transactions at ${merchant}: ${frequency.recent} vs average ${frequency.average.toFixed(1)}`,
            confidence: 0.8,
            suggestedAction: 'Check for duplicate transactions or subscription changes',
          });
        }
      }
    });

    return anomalies;
  }

  private static detectCategoryAnomalies(transactions: Transaction[]): AnomalyDetection[] {
    const anomalies: AnomalyDetection[] = [];
    // Implementation for category-based anomaly detection
    return anomalies;
  }

  private static detectTimingAnomalies(transactions: Transaction[]): AnomalyDetection[] {
    const anomalies: AnomalyDetection[] = [];
    // Implementation for timing-based anomaly detection
    return anomalies;
  }

  // Utility methods
  private static analyzeCategoryTrends(transactions: Transaction[]): Record<string, any> {
    const categoryData: Record<string, any> = {};
    
    transactions.forEach(transaction => {
      if (!categoryData[transaction.category]) {
        categoryData[transaction.category] = {
          amounts: [],
          dates: [],
          merchants: new Set(),
        };
      }
      
      categoryData[transaction.category].amounts.push(Math.abs(transaction.amount));
      categoryData[transaction.category].dates.push(transaction.date);
      if (transaction.merchant) {
        categoryData[transaction.category].merchants.add(transaction.merchant);
      }
    });

    return categoryData;
  }

  private static calculateTrend(amounts: number[]): { direction: 'increasing' | 'decreasing' | 'stable'; rate: number } {
    if (amounts.length < 3) return { direction: 'stable', rate: 0 };

    const recentAvg = amounts.slice(-3).reduce((a, b) => a + b, 0) / 3;
    const olderAvg = amounts.slice(0, -3).reduce((a, b) => a + b, 0) / (amounts.length - 3);
    
    const changeRate = Math.abs(recentAvg - olderAvg) / olderAvg;
    
    if (changeRate < 0.05) return { direction: 'stable', rate: 0 };
    
    return {
      direction: recentAvg > olderAvg ? 'increasing' : 'decreasing',
      rate: changeRate,
    };
  }

  private static getSeasonalFactor(category: string): number {
    const currentMonth = new Date().getMonth();
    const monthName = new Date().toLocaleString('default', { month: 'long' }).toLowerCase();
    
    // Check seasonal patterns
    const seasonalPattern = this.SEASONAL_PATTERNS[monthName as keyof typeof this.SEASONAL_PATTERNS];
    if (seasonalPattern && seasonalPattern[category as keyof typeof seasonalPattern]) {
      return seasonalPattern[category as keyof typeof seasonalPattern];
    }

    return 1; // No seasonal adjustment
  }

  private static calculateMovingAverage(values: number[], window: number): number {
    if (values.length === 0) return 0;
    const relevantValues = values.slice(-window);
    return relevantValues.reduce((a, b) => a + b, 0) / relevantValues.length;
  }

  private static calculateConfidence(amounts: number[]): number {
    if (amounts.length < 3) return 0.5;
    
    const mean = amounts.reduce((a, b) => a + b, 0) / amounts.length;
    const variance = amounts.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / amounts.length;
    const coefficientOfVariation = Math.sqrt(variance) / mean;
    
    // Lower coefficient of variation = higher confidence
    return Math.max(0.3, Math.min(0.95, 1 - coefficientOfVariation));
  }

  private static getTimeMultiplier(timeframe: string): number {
    switch (timeframe) {
      case '1_month': return 1;
      case '3_months': return 3;
      case '6_months': return 6;
      case '1_year': return 12;
      default: return 1;
    }
  }

  private static getMonthsFromTimeframe(timeframe: string): number {
    return this.getTimeMultiplier(timeframe);
  }

  private static calculateAverageMonthlyIncome(transactions: Transaction[]): number {
    const incomeTransactions = transactions.filter(t => t.type === 'credit');
    if (incomeTransactions.length === 0) return 0;
    
    const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);
    const months = this.getUniqueMonths(incomeTransactions);
    
    return totalIncome / Math.max(1, months);
  }

  private static calculateAverageMonthlyExpenses(transactions: Transaction[]): number {
    const expenseTransactions = transactions.filter(t => t.type === 'debit');
    if (expenseTransactions.length === 0) return 0;
    
    const totalExpenses = expenseTransactions.reduce((sum, t) => sum + Math.abs(t.amount), 0);
    const months = this.getUniqueMonths(expenseTransactions);
    
    return totalExpenses / Math.max(1, months);
  }

  private static getUniqueMonths(transactions: Transaction[]): number {
    const months = new Set();
    transactions.forEach(t => {
      const date = new Date(t.date);
      months.add(`${date.getFullYear()}-${date.getMonth()}`);
    });
    return months.size;
  }

  private static calculateIncomeVariability(transactions: Transaction[]): number {
    // Implementation for income variability calculation
    return 0.1; // Placeholder
  }

  private static calculateExpenseVariability(transactions: Transaction[]): number {
    // Implementation for expense variability calculation
    return 0.15; // Placeholder
  }

  private static getIncomeSeasonality(month: number): number {
    // December often has bonuses in South Africa
    if (month === 12) return 1.3;
    return 1;
  }

  private static getExpenseSeasonality(month: number): number {
    // December and January typically have higher expenses
    if (month === 12) return 1.4;
    if (month === 1) return 1.2;
    return 1;
  }

  private static assessCashflowRisk(balance: number, monthlyExpenses: number): 'low' | 'medium' | 'high' {
    const monthsOfExpenses = balance / monthlyExpenses;
    
    if (monthsOfExpenses >= 6) return 'low';
    if (monthsOfExpenses >= 3) return 'medium';
    return 'high';
  }

  private static generateCashflowAlerts(balance: number, monthlyExpenses: number, month: number): string[] {
    const alerts: string[] = [];
    const monthsOfExpenses = balance / monthlyExpenses;
    
    if (monthsOfExpenses < 1) {
      alerts.push('Critical: Less than 1 month of expenses remaining');
    } else if (monthsOfExpenses < 3) {
      alerts.push('Warning: Less than 3 months of emergency fund');
    }
    
    if (balance < 0) {
      alerts.push('Account may go into overdraft');
    }
    
    return alerts;
  }

  private static calculateHistoricalSavingsRate(transactions: Transaction[]): {
    averageMonthly: number;
    rate: number;
    consistency: number;
  } {
    // Implementation for historical savings rate calculation
    return {
      averageMonthly: 2000,
      rate: 0.15,
      consistency: 0.8,
    };
  }

  private static calculateSavingsTrend(transactions: Transaction[]): { direction: 'increasing' | 'decreasing' | 'stable'; rate: number } {
    // Implementation for savings trend calculation
    return { direction: 'stable', rate: 0 };
  }

  private static identifySpendingFactors(category: string, data: any): string[] {
    const factors: string[] = [];
    
    if (data.merchants.size > 5) {
      factors.push('Multiple merchants');
    }
    
    if (data.amounts.length > 10) {
      factors.push('Frequent transactions');
    }
    
    return factors;
  }

  private static generateForecastRecommendations(
    spendingPredictions: SpendingPrediction[],
    cashflowPredictions: CashflowPrediction[]
  ): string[] {
    const recommendations: string[] = [];
    
    // Check for high-risk cashflow periods
    const riskyCashflow = cashflowPredictions.filter(p => p.riskLevel === 'high');
    if (riskyCashflow.length > 0) {
      recommendations.push('Consider building emergency fund before predicted low-balance periods');
    }
    
    // Check for increasing spending trends
    const increasingSpending = spendingPredictions.filter(p => p.trend === 'increasing');
    if (increasingSpending.length > 2) {
      recommendations.push('Review budget limits for categories with increasing spending trends');
    }
    
    return recommendations;
  }

  private static calculateCategoryStatistics(transactions: Transaction[]): Record<string, { mean: number; stdDev: number }> {
    const stats: Record<string, { mean: number; stdDev: number }> = {};
    
    const categoryGroups = transactions.reduce((groups, t) => {
      if (!groups[t.category]) groups[t.category] = [];
      groups[t.category].push(Math.abs(t.amount));
      return groups;
    }, {} as Record<string, number[]>);

    Object.entries(categoryGroups).forEach(([category, amounts]) => {
      const mean = amounts.reduce((a, b) => a + b, 0) / amounts.length;
      const variance = amounts.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / amounts.length;
      const stdDev = Math.sqrt(variance);
      
      stats[category] = { mean, stdDev };
    });

    return stats;
  }

  private static calculateMerchantFrequency(transactions: Transaction[]): Record<string, { recent: number; average: number }> {
    const frequency: Record<string, { recent: number; average: number }> = {};
    
    const now = Date.now();
    const oneMonthAgo = now - (30 * 24 * 60 * 60 * 1000);
    const threeMonthsAgo = now - (90 * 24 * 60 * 60 * 1000);

    transactions.forEach(t => {
      if (!t.merchant) return;
      
      if (!frequency[t.merchant]) {
        frequency[t.merchant] = { recent: 0, average: 0 };
      }
      
      if (t.date >= oneMonthAgo) {
        frequency[t.merchant].recent++;
      }
      
      if (t.date >= threeMonthsAgo) {
        frequency[t.merchant].average += 1/3; // Average per month over 3 months
      }
    });

    return frequency;
  }
}
