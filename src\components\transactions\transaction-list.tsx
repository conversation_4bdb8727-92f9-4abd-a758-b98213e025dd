"use client";

import { formatCurrency, formatDate } from "@/lib/utils";
import { Transaction } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ShoppingCart, 
  Car, 
  Gamepad2, 
  Zap, 
  Heart, 
  GraduationCap,
  ShoppingBag,
  UtensilsCrossed,
  Plane,
  Shield,
  TrendingUp,
  Banknote,
  ArrowRightLeft,
  CreditCard,
  MoreHorizontal
} from "lucide-react";

interface TransactionListProps {
  transactions: Transaction[];
  showAccount?: boolean;
  limit?: number;
}

const categoryIcons = {
  groceries: ShoppingCart,
  transport: Car,
  entertainment: Gamepad2,
  utilities: Zap,
  healthcare: Heart,
  education: GraduationCap,
  shopping: ShoppingBag,
  dining: UtensilsCrossed,
  travel: Plane,
  insurance: Shield,
  investments: TrendingUp,
  income: Banknote,
  transfers: ArrowRightLeft,
  fees: CreditCard,
  other: MoreHorizontal,
};

const categoryColors = {
  groceries: "bg-green-100 text-green-800",
  transport: "bg-blue-100 text-blue-800",
  entertainment: "bg-purple-100 text-purple-800",
  utilities: "bg-yellow-100 text-yellow-800",
  healthcare: "bg-red-100 text-red-800",
  education: "bg-indigo-100 text-indigo-800",
  shopping: "bg-pink-100 text-pink-800",
  dining: "bg-orange-100 text-orange-800",
  travel: "bg-cyan-100 text-cyan-800",
  insurance: "bg-gray-100 text-gray-800",
  investments: "bg-emerald-100 text-emerald-800",
  income: "bg-green-100 text-green-800",
  transfers: "bg-blue-100 text-blue-800",
  fees: "bg-red-100 text-red-800",
  other: "bg-gray-100 text-gray-800",
};

export function TransactionList({ transactions, showAccount = false, limit }: TransactionListProps) {
  const displayTransactions = limit ? transactions.slice(0, limit) : transactions;

  if (transactions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            No transactions found
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Transactions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {displayTransactions.map((transaction) => {
          const IconComponent = categoryIcons[transaction.category];
          const colorClass = categoryColors[transaction.category];
          
          return (
            <div
              key={transaction.id}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colorClass}`}>
                  <IconComponent className="w-5 h-5" />
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {transaction.description}
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Badge variant="secondary" className="text-xs">
                      {transaction.category}
                    </Badge>
                    <span>•</span>
                    <span>{formatDate(new Date(transaction.date))}</span>
                    {transaction.merchant && (
                      <>
                        <span>•</span>
                        <span>{transaction.merchant}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className={`font-semibold ${
                  transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {transaction.type === 'credit' ? '+' : '-'}
                  {formatCurrency(Math.abs(transaction.amount), transaction.currency)}
                </div>
                <div className="text-sm text-gray-500 capitalize">
                  {transaction.type}
                </div>
              </div>
            </div>
          );
        })}
        
        {limit && transactions.length > limit && (
          <div className="text-center pt-4">
            <button className="text-blue-600 hover:text-blue-700 font-medium">
              View all {transactions.length} transactions
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
