import { ConvexError } from "convex/values";

// Type for Convex context with auth
type ConvexContext = {
  auth: {
    getUserIdentity: () => Promise<{
      tokenIdentifier: string;
      subject: string;
      name?: string;
      email?: string;
    } | null>;
  };
};

// Helper function to get the authenticated user ID
export async function getAuthenticatedUserId(ctx: ConvexContext) {
  const identity = await ctx.auth.getUserIdentity();
  
  if (!identity) {
    throw new ConvexError("Not authenticated");
  }
  
  return identity.subject;
}

// Helper function to check if the user is authenticated
export async function isAuthenticated(ctx: ConvexContext): Promise<boolean> {
  try {
    await getAuthenticatedUserId(ctx);
    return true;
  } catch {
    return false;
  }
}

// Helper function to verify the user has access to a specific resource
export async function verifyUserAccess(ctx: ConvexContext, userId: string): Promise<true> {
  const authenticatedUserId = await getAuthenticatedUserId(ctx);
  
  if (authenticatedUserId !== userId) {
    throw new ConvexError("Unauthorized access to resource");
  }
  
  return true;
}
