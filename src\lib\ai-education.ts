import { Transaction, TransactionCategory } from './types';

export interface EducationalContent {
  id: string;
  title: string;
  description: string;
  category: 'budgeting' | 'saving' | 'investing' | 'debt' | 'insurance' | 'taxes' | 'banking' | 'credit';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number; // in minutes
  content: string;
  keyTakeaways: string[];
  actionItems: string[];
  relatedTopics: string[];
  southAfricanContext: boolean;
}

export interface LearningPath {
  id: string;
  name: string;
  description: string;
  modules: EducationalContent[];
  estimatedDuration: number; // in hours
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
}

export interface ChatbotResponse {
  message: string;
  suggestions: string[];
  relatedContent?: EducationalContent[];
  actionable: boolean;
}

export class AIEducationEngine {
  private static readonly SOUTH_AFRICAN_CONTENT: EducationalContent[] = [
    {
      id: 'sa-banking-basics',
      title: 'Understanding South African Banking',
      description: 'Learn about the major banks, account types, and banking fees in South Africa',
      category: 'banking',
      difficulty: 'beginner',
      estimatedTime: 15,
      content: `
        South Africa has a well-developed banking system with several major players:
        
        **Major Banks:**
        - Standard Bank: Largest bank by assets
        - FirstRand (FNB): Known for innovation and digital banking
        - Absa: Strong retail presence
        - Nedbank: Focus on corporate and investment banking
        - Capitec: Low-cost banking model
        
        **Account Types:**
        - Cheque/Current Account: For daily transactions
        - Savings Account: Earns interest, limited transactions
        - Fixed Deposit: Higher interest, locked for specific period
        - Money Market: Higher interest for larger balances
        
        **Banking Fees:**
        - Monthly account fees: R50-R300
        - Transaction fees: R2-R15 per transaction
        - ATM fees: R8-R15 for other bank ATMs
        - Card fees: R15-R25 monthly
        
        **Tips to Reduce Banking Costs:**
        - Use your bank's ATMs
        - Choose accounts with lower monthly fees
        - Use digital banking to reduce transaction costs
        - Maintain minimum balances to waive fees
      `,
      keyTakeaways: [
        'Choose a bank that suits your needs and budget',
        'Understand all fees before opening an account',
        'Use digital banking to save on transaction costs',
        'Compare different account types and their benefits'
      ],
      actionItems: [
        'Compare banking packages from different banks',
        'Calculate your monthly banking costs',
        'Set up digital banking to reduce fees',
        'Review your bank statements monthly'
      ],
      relatedTopics: ['budgeting', 'saving'],
      southAfricanContext: true
    },
    {
      id: 'sa-tax-basics',
      title: 'Personal Tax in South Africa',
      description: 'Understanding PAYE, tax brackets, and deductions in the South African tax system',
      category: 'taxes',
      difficulty: 'intermediate',
      estimatedTime: 25,
      content: `
        **Tax Year:** March 1 to February 28/29
        
        **Tax Brackets (2024/25):**
        - R0 - R237,100: 18%
        - R237,101 - R370,500: 26%
        - R370,501 - R512,800: 31%
        - R512,801 - R673,000: 36%
        - R673,001 - R857,900: 39%
        - R857,901+: 41%
        
        **Key Deductions:**
        - Medical aid contributions
        - Retirement annuity contributions (up to 27.5% of income)
        - Travel allowance (if applicable)
        - Home office expenses (if working from home)
        
        **Tax-Free Savings Account (TFSA):**
        - Annual limit: R36,000
        - Lifetime limit: R500,000
        - No tax on interest, dividends, or capital gains
        
        **Important Dates:**
        - Tax season: July 1 - November 30
        - Provisional tax: August 31 and February 28
        - SARS eFiling registration required
      `,
      keyTakeaways: [
        'Understand your tax bracket and obligations',
        'Maximize tax-deductible contributions',
        'Use TFSA for tax-free growth',
        'Keep proper records for deductions'
      ],
      actionItems: [
        'Register for SARS eFiling',
        'Open a Tax-Free Savings Account',
        'Track deductible expenses throughout the year',
        'Consider increasing retirement contributions for tax benefits'
      ],
      relatedTopics: ['investing', 'saving'],
      southAfricanContext: true
    },
    {
      id: 'emergency-fund-sa',
      title: 'Building an Emergency Fund in South Africa',
      description: 'How to build and maintain an emergency fund considering South African economic conditions',
      category: 'saving',
      difficulty: 'beginner',
      estimatedTime: 20,
      content: `
        **Why Emergency Funds Matter in SA:**
        - High unemployment rate (29%+)
        - Economic volatility and rand fluctuations
        - Load shedding and infrastructure challenges
        - Limited social safety nets
        
        **How Much to Save:**
        - Minimum: 3 months of expenses
        - Recommended: 6-12 months of expenses
        - Consider higher amounts due to economic uncertainty
        
        **Where to Keep Emergency Funds:**
        - High-yield savings account
        - Money market account
        - 32-day notice deposit
        - Avoid: Fixed deposits (lack liquidity)
        
        **Building Your Emergency Fund:**
        1. Calculate monthly expenses
        2. Set a realistic savings target
        3. Automate transfers to emergency fund
        4. Start small - even R100/month helps
        5. Use windfalls (bonuses, tax refunds)
        
        **Best Accounts for Emergency Funds:**
        - Capitec Global One: Competitive interest
        - FNB Money Maximizer: Tiered interest rates
        - Standard Bank PureSave: No fees, good interest
        - Nedbank Money Maximizer: High interest for larger balances
      `,
      keyTakeaways: [
        'Emergency funds are crucial in South Africa\'s economic climate',
        'Aim for 6-12 months of expenses',
        'Keep funds accessible but earning interest',
        'Build gradually and consistently'
      ],
      actionItems: [
        'Calculate your monthly essential expenses',
        'Open a dedicated emergency fund account',
        'Set up automatic monthly transfers',
        'Review and adjust your target amount annually'
      ],
      relatedTopics: ['budgeting', 'banking'],
      southAfricanContext: true
    }
  ];

  static getPersonalizedContent(
    userProfile: {
      experience: 'beginner' | 'intermediate' | 'advanced';
      interests: string[];
      financialGoals: string[];
    },
    transactions: Transaction[] = []
  ): EducationalContent[] {
    let recommendations: EducationalContent[] = [];
    
    // Analyze spending patterns to suggest relevant content
    if (transactions.length > 0) {
      const categorySpending = this.analyzeSpendingPatterns(transactions);
      
      // High transport costs -> suggest transport budgeting
      if (categorySpending.transport > 2000) {
        recommendations.push(this.getContentById('transport-budgeting'));
      }
      
      // High dining costs -> suggest meal planning
      if (categorySpending.dining > 1500) {
        recommendations.push(this.getContentById('meal-planning-savings'));
      }
      
      // Low savings rate -> suggest emergency fund content
      const savingsRate = this.calculateSavingsRate(transactions);
      if (savingsRate < 0.1) {
        recommendations.push(this.getContentById('emergency-fund-sa'));
      }
    }
    
    // Add content based on experience level
    const levelContent = this.SOUTH_AFRICAN_CONTENT.filter(
      content => content.difficulty === userProfile.experience
    );
    
    recommendations.push(...levelContent.slice(0, 3));
    
    // Remove duplicates
    return recommendations.filter((content, index, self) => 
      index === self.findIndex(c => c.id === content.id)
    );
  }

  static generateLearningPath(
    userGoals: string[],
    currentLevel: 'beginner' | 'intermediate' | 'advanced'
  ): LearningPath {
    const pathModules: EducationalContent[] = [];
    const addedModuleIds = new Set<string>();
    
    // Helper function to add module if not already added
    const addModuleIfNotExists = (id: string) => {
      if (!addedModuleIds.has(id)) {
        const module = this.getContentById(id);
        pathModules.push(module);
        addedModuleIds.add(id);
      }
    };
    
    // Always start with basics for beginners
    if (currentLevel === 'beginner') {
      addModuleIfNotExists('sa-banking-basics');
      addModuleIfNotExists('emergency-fund-sa');
    }
    
    // Add goal-specific content
    if (userGoals.includes('save_money')) {
      addModuleIfNotExists('emergency-fund-sa');
    }
    
    if (userGoals.includes('invest')) {
      addModuleIfNotExists('sa-tax-basics');
    }
    
    const totalTime = pathModules.reduce((sum, module) => sum + module.estimatedTime, 0);
    
    return {
      id: `path-${Date.now()}`,
      name: 'Personalized Financial Journey',
      description: 'A customized learning path based on your goals and experience',
      modules: pathModules,
      estimatedDuration: Math.ceil(totalTime / 60),
      difficulty: currentLevel,
      prerequisites: currentLevel === 'beginner' ? [] : ['Basic financial knowledge']
    };
  }

  static chatbotResponse(
    question: string,
    userContext: {
      transactions?: Transaction[];
      goals?: string[];
      experience?: string;
    } = {}
  ): ChatbotResponse {
    const lowerQuestion = question.toLowerCase();
    
    // Banking questions
    if (lowerQuestion.includes('bank') || lowerQuestion.includes('account')) {
      return {
        message: "I can help you understand South African banking! The major banks are Standard Bank, FNB, Absa, Nedbank, and Capitec. Each offers different account types with varying fees. Would you like to know about specific account types or how to reduce banking costs?",
        suggestions: [
          "Tell me about different account types",
          "How can I reduce banking fees?",
          "Which bank is best for students?",
          "What are the major banks in SA?"
        ],
        relatedContent: [this.getContentById('sa-banking-basics')],
        actionable: true
      };
    }
    
    // Emergency fund questions
    if (lowerQuestion.includes('emergency') || lowerQuestion.includes('save')) {
      return {
        message: "Building an emergency fund is crucial in South Africa's economic climate. I recommend saving 6-12 months of expenses due to high unemployment and economic volatility. Start with even R100/month and build gradually.",
        suggestions: [
          "How much should I save for emergencies?",
          "Where should I keep my emergency fund?",
          "How do I start saving with a tight budget?",
          "What accounts offer the best interest rates?"
        ],
        relatedContent: [this.getContentById('emergency-fund-sa')],
        actionable: true
      };
    }
    
    // Tax questions
    if (lowerQuestion.includes('tax') || lowerQuestion.includes('sars')) {
      return {
        message: "South African tax can be complex, but understanding the basics helps you save money. The tax year runs from March to February, and you can reduce your tax through retirement contributions and TFSA investments. Are you looking for information about tax brackets, deductions, or filing?",
        suggestions: [
          "What are the current tax brackets?",
          "How can I reduce my tax liability?",
          "Tell me about Tax-Free Savings Accounts",
          "When do I need to file my tax return?"
        ],
        relatedContent: [this.getContentById('sa-tax-basics')],
        actionable: true
      };
    }
    
    // Default response
    return {
      message: "I'm here to help with your financial questions! I specialize in South African financial topics including banking, saving, investing, taxes, and budgeting. What would you like to learn about?",
      suggestions: [
        "How do I start budgeting?",
        "Tell me about South African banks",
        "How much should I save for emergencies?",
        "What are Tax-Free Savings Accounts?"
      ],
      actionable: false
    };
  }

  private static getContentById(id: string): EducationalContent {
    return this.SOUTH_AFRICAN_CONTENT.find(content => content.id === id) || this.SOUTH_AFRICAN_CONTENT[0];
  }

  private static analyzeSpendingPatterns(transactions: Transaction[]): Record<TransactionCategory, number> {
    const spending: Record<string, number> = {};
    
    transactions
      .filter(t => t.type === 'debit')
      .forEach(t => {
        spending[t.category] = (spending[t.category] || 0) + t.amount;
      });
    
    return spending as Record<TransactionCategory, number>;
  }

  private static calculateSavingsRate(transactions: Transaction[]): number {
    const income = transactions
      .filter(t => t.type === 'credit')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const expenses = transactions
      .filter(t => t.type === 'debit')
      .reduce((sum, t) => sum + t.amount, 0);
    
    return income > 0 ? (income - expenses) / income : 0;
  }

  static getAllContent(): EducationalContent[] {
    return this.SOUTH_AFRICAN_CONTENT;
  }

  static getContentByCategory(category: EducationalContent['category']): EducationalContent[] {
    return this.SOUTH_AFRICAN_CONTENT.filter(content => content.category === category);
  }

  static searchContent(query: string): EducationalContent[] {
    const lowerQuery = query.toLowerCase();
    return this.SOUTH_AFRICAN_CONTENT.filter(content =>
      content.title.toLowerCase().includes(lowerQuery) ||
      content.description.toLowerCase().includes(lowerQuery) ||
      content.content.toLowerCase().includes(lowerQuery)
    );
  }
}
