// Stitch API integration utilities
export interface StitchConfig {
  clientId: string;
  clientSecret: string;
  apiUrl: string;
  redirectUrl: string;
}

export interface StitchAccount {
  id: string;
  name: string;
  accountType: "checking" | "savings" | "credit";
  bankName: string;
  balance: number;
  currency: string;
}

export interface StitchTransaction {
  id: string;
  amount: number;
  currency: string;
  description: string;
  date: string;
  type: "debit" | "credit";
  merchant?: string;
  category?: string;
}

export interface StitchAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export interface StitchWebhookEvent {
  id: string;
  type: 'account.updated' | 'transaction.created' | 'payment.completed' | 'payment.failed';
  data: any;
  timestamp: string;
}

export interface StitchPaymentRequest {
  amount: number;
  currency: string;
  recipientAccountId: string;
  reference: string;
  description?: string;
  beneficiaryName?: string;
  beneficiaryReference?: string;
}

export interface StitchBankInfo {
  id: string;
  name: string;
  logo?: string;
  supportedAccountTypes: string[];
  isActive: boolean;
}

export class StitchAPI {
  private config: StitchConfig;
  private accessToken?: string;
  private tokenExpiry?: number;

  constructor(config: StitchConfig) {
    this.config = config;
  }

  private async getAccessToken(): Promise<string> {
    if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.accessToken;
    }

    const response = await fetch(`${this.config.apiUrl}/connect/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        scope: 'accounts transactions payments',
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get access token: ${response.statusText}`);
    }

    const data: StitchAuthResponse = await response.json();
    this.accessToken = data.access_token;
    this.tokenExpiry = Date.now() + (data.expires_in * 1000) - 60000; // 1 minute buffer

    return this.accessToken;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const token = await this.getAccessToken();
    
    const response = await fetch(`${this.config.apiUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`Stitch API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async getAuthorizationUrl(userId: string, scopes: string[] = ['accounts', 'transactions']): Promise<string> {
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      response_type: 'code',
      redirect_uri: this.config.redirectUrl,
      scope: scopes.join(' '),
      state: userId, // Use userId as state for security
    });

    return `${this.config.apiUrl}/connect/authorize?${params.toString()}`;
  }

  async exchangeCodeForToken(code: string): Promise<StitchAuthResponse> {
    const response = await fetch(`${this.config.apiUrl}/connect/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        code: code,
        redirect_uri: this.config.redirectUrl,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to exchange code for token: ${response.statusText}`);
    }

    return response.json();
  }

  async getAccounts(userToken: string): Promise<StitchAccount[]> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetAccounts {
            user {
              accounts {
                id
                name
                accountType
                bankName
                balance {
                  amount
                  currency
                }
              }
            }
          }
        `,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch accounts: ${response.statusText}`);
    }

    const data = await response.json();
    
    return data.data.user.accounts.map((account: any) => ({
      id: account.id,
      name: account.name,
      accountType: account.accountType.toLowerCase(),
      bankName: account.bankName,
      balance: account.balance.amount,
      currency: account.balance.currency,
    }));
  }

  async getTransactions(userToken: string, accountId: string, limit: number = 100): Promise<StitchTransaction[]> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetTransactions($accountId: ID!, $limit: Int!) {
            user {
              account(id: $accountId) {
                transactions(first: $limit) {
                  edges {
                    node {
                      id
                      amount {
                        amount
                        currency
                      }
                      description
                      date
                      type
                      merchant
                      category
                    }
                  }
                }
              }
            }
          }
        `,
        variables: {
          accountId,
          limit,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch transactions: ${response.statusText}`);
    }

    const data = await response.json();
    
    return data.data.user.account.transactions.edges.map((edge: any) => ({
      id: edge.node.id,
      amount: edge.node.amount.amount,
      currency: edge.node.amount.currency,
      description: edge.node.description,
      date: edge.node.date,
      type: edge.node.type.toLowerCase(),
      merchant: edge.node.merchant,
      category: edge.node.category,
    }));
  }

  async initiatePayment(userToken: string, paymentData: StitchPaymentRequest): Promise<{ paymentId: string; status: string }> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          mutation InitiatePayment($input: PaymentInput!) {
            initiatePayment(input: $input) {
              id
              status
              reference
              amount {
                amount
                currency
              }
            }
          }
        `,
        variables: {
          input: paymentData,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to initiate payment: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      paymentId: data.data.initiatePayment.id,
      status: data.data.initiatePayment.status,
    };
  }

  async getPaymentStatus(userToken: string, paymentId: string): Promise<{ status: string; details: any }> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetPayment($paymentId: ID!) {
            payment(id: $paymentId) {
              id
              status
              amount {
                amount
                currency
              }
              reference
              createdAt
              updatedAt
            }
          }
        `,
        variables: {
          paymentId,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get payment status: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      status: data.data.payment.status,
      details: data.data.payment,
    };
  }

  async getSupportedBanks(): Promise<StitchBankInfo[]> {
    const response = await this.makeRequest('/banks');

    return response.banks.map((bank: any) => ({
      id: bank.id,
      name: bank.name,
      logo: bank.logo,
      supportedAccountTypes: bank.supportedAccountTypes || ['checking', 'savings'],
      isActive: bank.isActive !== false,
    }));
  }

  async refreshAccountBalance(userToken: string, accountId: string): Promise<{ balance: number; currency: string }> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query RefreshBalance($accountId: ID!) {
            user {
              account(id: $accountId) {
                id
                balance {
                  amount
                  currency
                }
                lastUpdated
              }
            }
          }
        `,
        variables: {
          accountId,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to refresh account balance: ${response.statusText}`);
    }

    const data = await response.json();
    const account = data.data.user.account;

    return {
      balance: account.balance.amount,
      currency: account.balance.currency,
    };
  }

  async setupWebhook(webhookUrl: string, events: string[]): Promise<{ webhookId: string }> {
    const response = await this.makeRequest('/webhooks', {
      method: 'POST',
      body: JSON.stringify({
        url: webhookUrl,
        events,
        active: true,
      }),
    });

    return {
      webhookId: response.id,
    };
  }

  async verifyWebhookSignature(payload: string, signature: string, secret: string): Promise<boolean> {
    const crypto = await import('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');

    return `sha256=${expectedSignature}` === signature;
  }

  async processWebhookEvent(event: StitchWebhookEvent): Promise<void> {
    // This method would be called by your webhook endpoint
    // to process incoming events from Stitch
    console.log('Processing webhook event:', event.type, event.id);

    switch (event.type) {
      case 'account.updated':
        // Handle account balance updates
        break;
      case 'transaction.created':
        // Handle new transactions
        break;
      case 'payment.completed':
        // Handle completed payments
        break;
      case 'payment.failed':
        // Handle failed payments
        break;
      default:
        console.warn('Unknown webhook event type:', event.type);
    }
  }

  // South African specific features
  async validateSouthAfricanBankAccount(accountNumber: string, bankCode: string): Promise<boolean> {
    // Implement South African bank account validation
    // This would typically involve checking the account number format
    // and validating against the specific bank's requirements

    if (!accountNumber || !bankCode) {
      return false;
    }

    // Basic validation - in a real implementation, you'd have more sophisticated checks
    const accountNumberRegex = /^\d{8,11}$/; // SA account numbers are typically 8-11 digits
    return accountNumberRegex.test(accountNumber);
  }

  async getEFTDetails(userToken: string, accountId: string): Promise<{
    accountNumber: string;
    branchCode: string;
    bankName: string;
    accountHolder: string;
  }> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetEFTDetails($accountId: ID!) {
            user {
              account(id: $accountId) {
                id
                accountNumber
                branchCode
                bankName
                accountHolder
              }
            }
          }
        `,
        variables: {
          accountId,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get EFT details: ${response.statusText}`);
    }

    const data = await response.json();
    const account = data.data.user.account;

    return {
      accountNumber: account.accountNumber,
      branchCode: account.branchCode,
      bankName: account.bankName,
      accountHolder: account.accountHolder,
    };
  }
}

// Create a singleton instance
export const stitchAPI = new StitchAPI({
  clientId: process.env.STITCH_CLIENT_ID || '',
  clientSecret: process.env.STITCH_CLIENT_SECRET || '',
  apiUrl: process.env.STITCH_API_URL || 'https://api.stitch.money',
  redirectUrl: process.env.NEXT_PUBLIC_STITCH_REDIRECT_URL || 'http://localhost:3000/api/stitch/callback',
});

// Helper function to categorize transactions using AI/rules
export function categorizeTransaction(description: string, merchant?: string): string {
  const desc = description.toLowerCase();
  const merch = merchant?.toLowerCase() || '';
  
  // Grocery stores
  if (desc.includes('shoprite') || desc.includes('pick n pay') || desc.includes('checkers') || 
      desc.includes('woolworths') || desc.includes('spar') || merch.includes('grocery')) {
    return 'groceries';
  }
  
  // Transport
  if (desc.includes('uber') || desc.includes('bolt') || desc.includes('taxi') || 
      desc.includes('petrol') || desc.includes('fuel') || desc.includes('gautrain')) {
    return 'transport';
  }
  
  // Utilities
  if (desc.includes('eskom') || desc.includes('city power') || desc.includes('water') || 
      desc.includes('electricity') || desc.includes('municipal')) {
    return 'utilities';
  }
  
  // Entertainment
  if (desc.includes('netflix') || desc.includes('dstv') || desc.includes('cinema') || 
      desc.includes('movie') || desc.includes('showmax')) {
    return 'entertainment';
  }
  
  // Dining
  if (desc.includes('restaurant') || desc.includes('mcdonald') || desc.includes('kfc') || 
      desc.includes('steers') || desc.includes('nando')) {
    return 'dining';
  }
  
  // Healthcare
  if (desc.includes('pharmacy') || desc.includes('clicks') || desc.includes('dischem') || 
      desc.includes('doctor') || desc.includes('medical')) {
    return 'healthcare';
  }
  
  // Default category
  return 'other';
}
