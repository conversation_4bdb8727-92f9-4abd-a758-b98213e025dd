import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import type { Id } from '../../convex/_generated/dataModel';
// Define the BankAccount type to match the Convex schema
type BankAccount = {
  _id: Id<"bankAccounts">;
  _creationTime: number;
  updatedAt?: number;
  isActive: boolean;
  lastSynced: number;
  createdAt: number;
  userId: Id<"users">;
  accountId: string;
  accountName: string;
  accountType: "checking" | "savings" | "credit";
  balance: number;
  currency: string;
};

export type { BankAccount };

export function useBankAccounts(userId: Id<"users">) {
  // Get all active bank accounts for the user
  const accounts = useQuery(api.bankAccounts.getBankAccountsByUser, { userId }) || [];
  
  
  // Get total balance
  const totalBalance = useQuery(api.bankAccounts.getTotalBalance, { userId });
  
  // Mutations
  const createAccount = useMutation(api.bankAccounts.createBankAccount);
  const updateBalance = useMutation(api.bankAccounts.updateBankAccountBalance);
  const deactivateAccount = useMutation(api.bankAccounts.deactivateBankAccount);
  
  // Helper functions
  const getAccount = (accountId: Id<"bankAccounts">) => 
    accounts?.find((account: BankAccount) => account._id === accountId);
  
  const getAccountsByType = (type: "checking" | "savings" | "credit") => 
    accounts?.filter((account: BankAccount) => account.accountType === type) || [];
  
  return {
    // Data
    accounts,
    totalBalance: totalBalance?.total || 0,
    isLoading: accounts === undefined,
    
    // Helpers
    getAccount,
    getAccountsByType,
    
    // Mutations
    createAccount: async (data: Omit<BankAccount, '_id' | '_creationTime' | 'isActive' | 'lastSynced' | 'createdAt' | 'updatedAt'>) => {
      return await createAccount({
        ...data,
        userId,
        bankName: ''
      });
    },
    
    updateBalance: async (accountId: Id<"bankAccounts">, balance: number) => {
      return await updateBalance({
        accountId,
        balance,
      });
    },
    
    deactivateAccount: async (accountId: Id<"bankAccounts">, force: boolean = false) => {
      return await deactivateAccount({
        accountId,
        force,
      });
    },
  };
}

// Hook for a single account
export function useBankAccount(accountId: Id<"bankAccounts"> | null) {
  const account = useQuery(
    api.bankAccounts.getBankAccount, 
    accountId ? { accountId } : 'skip'
  ) as BankAccount | undefined;
  
  const updateBalance = useMutation(api.bankAccounts.updateBankAccountBalance);
  const deactivateAccount = useMutation(api.bankAccounts.deactivateBankAccount);
  
  return {
    account,
    updateBalance: async (balance: number) => {
      if (!accountId) throw new Error("No account ID provided");
      return await updateBalance({ accountId, balance });
    },
    deactivateAccount: async (force = false) => {
      if (!accountId) throw new Error("No account ID provided");
      return await deactivateAccount({ accountId, force });
    },
    isLoading: account === undefined,
  };
}
